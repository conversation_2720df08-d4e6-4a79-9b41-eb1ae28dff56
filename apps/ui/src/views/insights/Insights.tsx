import { useContext, useEffect, useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { PageContent, Button } from "../../ui";
import { SingleSelect } from "../../ui/form/SingleSelect";
import api from "../../api";
import { Insight } from "../../types";
import { LocationContext } from "../../contexts";
import InsightCard from "../../ui/InsightCard";
import Spinner from "../../ui/Spinner";
import "./Insights.css";
import SmokeyIcon from "../../assets/smokey_icon.png";
import { useNavigate } from "react-router-dom";
import useValidateSubscription from "../../hooks/useValidateSubscription";
import { AGENTS } from "../../utils/agents";

// Agent information from our configuration
// const AGENTS = {
//   SMOKEY: { icon: "🌿", name: "<PERSON><PERSON>", role: "AI Budtender" },
//   CRAIG: { icon: "📈", name: "<PERSON>", role: "Marketing Automation" },
//   POPS: { icon: "📊", name: "<PERSON><PERSON>", role: "Business Intelligence" },
//   EZAL: { icon: "🔍", name: "Ezal", role: "Market Intelligence" },
//   "MONEY MIKE": { icon: "💰", name: "Money Mike", role: "Financial Analytics" },
//   "MRS. PARKER": { icon: "👑", name: "Mrs. Parker", role: "Customer Relations" },
//   DEEBO: { icon: "🔒", name: "Deebo", role: "Compliance & Security" },
// };

export default function Insights() {
  const { t } = useTranslation();
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(true);
  const [location] = useContext(LocationContext);
  const [selectedModel, setSelectedModel] = useState("gpt-4o-mini");
  const { isSubscribed, isPro } = useValidateSubscription();
  const navigate = useNavigate();

  const loadInsights = useCallback(async () => {
    setLoading(true);
    try {
      const data = await api.insights.get(location.id);
      setInsights(data);
    } catch (error) {
      console.error("Error loading insights:", error);
    }
    setLoading(false);
  }, [location]);

  useEffect(() => {
    loadInsights();
  }, [loadInsights]);

  const handleGenerate = async () => {
    try {
      setLoading(true);
      await api.insights.generate(location.id, selectedModel);
      await loadInsights(); // Refresh the list after generation
      setLoading(false);
    } catch (error) {
      console.error("Error generating insights:", error);
      setLoading(false);
    }
  };

  const handleUpgrade = () => {
    navigate("/upgrade");
  };

  // System explanation component
  const SystemExplanation = () => (
    <div className="system-explanation">
      <h3 className="system-explanation-title">
        {t("how_it_works", "How Smokey Growth Engine Works")}
      </h3>
      <p className="system-explanation-description">
        {t(
          "system_description",
          "Our AI agents analyze your business data 24/7 to identify growth opportunities and automate actions that drive revenue."
        )}
      </p>
      <div className="agents-grid">
        {Object.entries(AGENTS).map(([key, agent]) => (
          <div key={key} className="agent-card">
            <span className="agent-icon">{agent.icon}</span>
            <div className="agent-info">
              <h4 className="agent-name">{agent.name}</h4>
              <p className="agent-role">{agent.role}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Empty state component
  const EmptyInsights = () => (
    <div className="empty-insights">
      <div className="empty-insights-content">
        <SystemExplanation />
        <Button
          onClick={handleGenerate}
          disabled={loading}
          className="generate-insights-button"
        >
          {loading ? (
            <Spinner size="small" />
          ) : (
            t("generate_first_insights", "Generate Your First Insights")
          )}
        </Button>
      </div>
    </div>
  );

  // Blur overlay component for premium content
  const UpgradeOverlay = () => (
    <div className="upgrade-overlay">
      <div className="upgrade-content">
        <h3>{t("unlock_more_insights", "Unlock More Insights")}</h3>
        <p>
          {t(
            "upgrade_to_see_more",
            "Upgrade your subscription to access all insights and grow your business further."
          )}
        </p>
        <Button onClick={handleUpgrade} className="upgrade-button">
          {t("upgrade_now", "Upgrade Now")}
        </Button>
      </div>
    </div>
  );

  const displayedInsights = isPro ? insights : insights.slice(0, 3);
  const hasMoreInsights = !isPro && insights.length > 3;

  return (
    <PageContent
      title={"Smokey Growth Engine"}
      desc={t(
        "growth_engine_subtitle",
        "AI-powered insights and automation for cannabis retail"
      )}
      actions={
        insights.length > 0 && (
          <div className="flex items-center gap-3">
            <Button disabled={loading} onClick={handleGenerate}>
              {loading ? (
                <Spinner size="small" />
              ) : (
                t("generate_new_insights", "Generate New Insights")
              )}
            </Button>
          </div>
        )
      }
    >
      {loading && insights.length === 0 ? (
        <div className="loading-container">
          <Spinner size="large" />
          <p className="loading-text">
            {t(
              "analyzing_with_agents",
              "Our AI agents are analyzing your data..."
            )}
          </p>
          <div className="loading-agents">
            {Object.entries(AGENTS)
              .slice(0, 4)
              .map(([key, agent]) => (
                <span key={key} className="loading-agent">
                  {agent.icon}
                </span>
              ))}
          </div>
        </div>
      ) : insights.length > 0 ? (
        <>
          <div className="insights-header">
            <p className="insights-header-text">
              {t(
                "insights_header",
                "Your AI agents have discovered the following opportunities:"
              )}
            </p>
          </div>
          <div className="insights-list">
            {displayedInsights.map((insight) => (
              <div key={insight.id}>
                <InsightCard insight={insight} onRefresh={loadInsights} />
              </div>
            ))}
            {hasMoreInsights && (
              <div className="insights-premium-container">
                <div className="premium-insights-blur">
                  {insights.slice(3, 4).map((insight) => (
                    <div key={insight.id} className="blurred-insight-wrapper">
                      <InsightCard insight={insight} onRefresh={loadInsights} />
                    </div>
                  ))}
                </div>
                <UpgradeOverlay />
              </div>
            )}
          </div>
        </>
      ) : (
        <EmptyInsights />
      )}
    </PageContent>
  );
}
