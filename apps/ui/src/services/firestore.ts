/**
 * MIGRATION STATUS: IN PROGRESS
 * We have migrated from Firebase to Supabase for retailer data.
 *
 * All original Firebase functions now call their Supabase equivalents:
 * - searchRetailers -> searchRetailersSupabase
 * - searchNearbyRetailers -> searchNearbyRetailersSupabase
 * - countRetailerProducts -> countRetailerProductsSupabase
 * - countProductsForRetailers -> countProductsForRetailersSupabase
 *
 * IMPORTANT: New code should use the Supabase versions directly.
 * The original functions will be removed in a future update.
 */

// Import only what we need for Google Places integration
import { miscService, PlacePrediction } from "../api/misc";
import Fuse, { IFuseOptions, FuseResult } from "fuse.js";

// Firebase imports are no longer needed - we use Supabase endpoints now
// (Keeping Fuse.js for sorting/filtering results)

export interface RetailerResult {
  id: string;
  dispensary_name: string;
  physical_address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  latitude: number;
  longitude: number;
  contact_phone: string;
  contact_email: string;
  website_url: string;
  serves_medical_users: boolean;
  serves_recreational_users: boolean;
  isFromOurDatabase: boolean; // Changed from true to boolean to support Google results
  productCount?: number;
  relevanceScore?: number; // Added for sorting by relevance
  place_id?: string; // Google place_id
  fuzzyScore?: number; // Added for Fuse.js score
  distance?: number; // Added for nearby searches
}

/**
 * Supabase version of counting products for a retailer
 */
export async function countRetailerProductsSupabase(
  retailerId: string
): Promise<number> {
  try {
    // Use fetch to call the Supabase-backed API endpoint through the misc controller
    const response = await fetch(
      `/api/misc/retailers/${retailerId}/products?limit=1`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token") || ""}`,
          "Content-Type": "application/json",
        },
      }
    );
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data.total_count || 0; // Assuming the API returns a total count
  } catch (error) {
    console.error(`Error counting products for retailer ${retailerId}:`, error);
    return 0;
  }
}

/**
 * Count products for a specific retailer
 * @param retailerId The ID of the retailer to count products for
 * @returns Number of products found
 */
export const countRetailerProducts = async (
  retailerId: string
): Promise<number> => {
  console.warn(
    "MIGRATION: countRetailerProducts is now using Supabase. Consider using countRetailerProductsSupabase directly."
  );
  return countRetailerProductsSupabase(retailerId);
};

/**
 * Supabase version of counting products for multiple retailers
 */
export const countProductsForRetailersSupabase = async (
  retailerIds: string[]
): Promise<Map<string, number>> => {
  try {
    const productCounts = new Map<string, number>();

    // Process in batches to avoid too many parallel requests
    const batchSize = 5;
    for (let i = 0; i < retailerIds.length; i += batchSize) {
      const batch = retailerIds.slice(i, i + batchSize);

      // Process batch in parallel
      const countPromises = batch.map(async (retailerId) => {
        try {
          const count = await countRetailerProductsSupabase(retailerId);
          return { retailerId, count };
        } catch (error) {
          console.error(
            `Error counting products for retailer ${retailerId}:`,
            error
          );
          return { retailerId, count: 0 };
        }
      });

      const results = await Promise.all(countPromises);

      // Add results to the map
      results.forEach(({ retailerId, count }) => {
        productCounts.set(retailerId, count);
      });
    }

    return productCounts;
  } catch (error) {
    console.error("Error batch counting products:", error);
    return new Map<string, number>();
  }
};

// Update the original countProductsForRetailers function to use Supabase
export const countProductsForRetailers = async (
  retailerIds: string[]
): Promise<Map<string, number>> => {
  console.warn(
    "MIGRATION: countProductsForRetailers is now using Supabase. Consider using countProductsForRetailersSupabase directly."
  );
  return countProductsForRetailersSupabase(retailerIds);
};

/**
 * Supabase version of the search retailers function
 */
export async function searchRetailersSupabase(
  searchQuery: string,
  includeProductCount: boolean = true
): Promise<RetailerResult[]> {
  try {
    // Use fetch to call the Supabase-backed API endpoint through the misc controller
    const response = await fetch(
      `/api/misc/retailers/search?query=${encodeURIComponent(searchQuery)}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token") || ""}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.retailers) {
      // Map Supabase retailer format to RetailerResult format
      const retailers = data.retailers.map((retailer: any) => ({
        id: retailer.retailer_id,
        dispensary_name: retailer.name || retailer.dispensary_name,
        physical_address: retailer.address || retailer.physical_address,
        city: retailer.city,
        state: retailer.state,
        zip_code: retailer.zip_code,
        country: retailer.country || "US",
        contact_phone: retailer.phone || retailer.contact_phone,
        contact_email: retailer.email || retailer.contact_email,
        website_url: retailer.website_url,
        latitude: parseFloat(retailer.latitude) || 0,
        longitude: parseFloat(retailer.longitude) || 0,
        productCount: retailer.product_count || 0,
        serves_medical_users: retailer.serves_medical_users || false,
        serves_recreational_users: retailer.serves_recreational_users || false,
        isFromOurDatabase: true,
      }));

      return retailers;
    }

    // Fallback to empty array if no results
    return [];
  } catch (error) {
    console.error("Error searching retailers:", error);
    return [];
  }
}

/**
 * Enhanced business search with word count logic and Google Places fallback
 */
export async function searchBusinessRetailersSupabase(
  searchQuery: string,
  includeProductCount: boolean = true
): Promise<RetailerResult[]> {
  try {
    // Use fetch to call the enhanced business search API endpoint
    const response = await fetch(
      `/api/misc/retailers/business-search?query=${encodeURIComponent(searchQuery)}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token") || ""}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.retailers) {
      // Map retailer format to RetailerResult format
      const retailers = data.retailers.map((retailer: any) => ({
        id: retailer.retailer_id || retailer.id,
        dispensary_name: retailer.name || retailer.dispensary_name,
        physical_address: retailer.address || retailer.physical_address,
        city: retailer.city,
        state: retailer.state,
        zip_code: retailer.zip_code,
        country: retailer.country || "US",
        contact_phone: retailer.phone || retailer.contact_phone,
        contact_email: retailer.email || retailer.contact_email,
        website_url: retailer.website_url,
        latitude: parseFloat(retailer.latitude) || 0,
        longitude: parseFloat(retailer.longitude) || 0,
        productCount: retailer.product_count || retailer.productCount || 0,
        serves_medical_users: retailer.serves_medical_users || false,
        serves_recreational_users: retailer.serves_recreational_users || false,
        isFromOurDatabase: retailer.isFromOurDatabase !== false, // Default to true unless explicitly false (for Google Places results)
      }));

      return retailers;
    }

    // Fallback to empty array if no results
    return [];
  } catch (error) {
    console.error("Error searching business retailers:", error);
    return [];
  }
}

/**
 * NEW: Supabase version of search retailers by location
 */
export async function searchRetailersByLocationSupabase(
  city: string,
  state: string,
  zip: string,
  radiusMiles: number = 30,
  page: number = 1,
  pageSize: number = 10
): Promise<RetailerResult[]> {
  try {
    const queryParams = new URLSearchParams({
      radius: radiusMiles.toString(),
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    if (city) queryParams.append("city", city);
    if (state) queryParams.append("state", state);
    if (zip) queryParams.append("zip", zip);

    const response = await fetch(
      `/api/misc/retailers/by-location?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth_token") || ""}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.retailers) {
      return data.retailers.map((retailer: any) => ({
        id: retailer.retailer_id,
        dispensary_name: retailer.name || retailer.dispensary_name,
        physical_address: retailer.address || retailer.physical_address,
        city: retailer.city,
        state: retailer.state,
        zip_code: retailer.zip_code,
        country: retailer.country || "US",
        contact_phone: retailer.phone || retailer.contact_phone,
        contact_email: retailer.email || retailer.contact_email,
        website_url: retailer.website_url,
        latitude: parseFloat(retailer.latitude) || 0,
        longitude: parseFloat(retailer.longitude) || 0,
        productCount: retailer.product_count || 0,
        serves_medical_users: retailer.serves_medical_users || false,
        serves_recreational_users: retailer.serves_recreational_users || false,
        isFromOurDatabase: true,
      }));
    }

    return [];
  } catch (error) {
    console.error("Error searching retailers by location:", error);
    return [];
  }
}

/**
 * Search for retailers in Google Places API
 */
export async function searchRetailersInGooglePlaces(
  searchQuery: string
): Promise<RetailerResult[]> {
  try {
    console.log("Searching Google Places for:", searchQuery);
    const response = await miscService.searchPlaces(searchQuery);

    if (
      !response ||
      !response.suggestions ||
      response.suggestions.length === 0
    ) {
      console.log("No results from Google Places");
      return [];
    }

    console.log(`Found ${response.suggestions.length} Google Places results`);

    // Convert Google Places results to RetailerResult format
    const googleResults = await Promise.all(
      response.suggestions.map(async (suggestion) => {
        try {
          const place = suggestion.placePrediction;

          // Get full details with Geocode to get coordinates
          const geocodeResponse = await miscService.geocodePlace(place.placeId);

          // Try multiple locations for coordinates (Google API can return different structures)
          let latitude = 0;
          let longitude = 0;

          if (geocodeResponse?.results?.[0]) {
            const result = geocodeResponse.results[0];

            // First try geometry.location (most common)
            if (result.geometry?.location) {
              latitude = result.geometry.location.lat || 0;
              longitude = result.geometry.location.lng || 0;
            }
            // Fallback to navigation_points if geometry.location is not available
            else if (result.navigation_points?.[0]?.location) {
              latitude = result.navigation_points[0].location.latitude || 0;
              longitude = result.navigation_points[0].location.longitude || 0;
            }

            console.log(
              `Geocoded ${place.structuredFormat.mainText.text}: lat=${latitude}, lng=${longitude}`
            );
          }

          return {
            id: place.placeId,
            place_id: place.placeId,
            dispensary_name: place.structuredFormat.mainText.text,
            physical_address: place.structuredFormat.secondaryText.text,
            city: "", // These fields aren't available directly from Google
            state: "",
            zip_code: "",
            country: "",
            latitude: latitude,
            longitude: longitude,
            contact_phone: "",
            contact_email: "",
            website_url: "",
            serves_medical_users: false,
            serves_recreational_users: false,
            isFromOurDatabase: false,
            relevanceScore: 50, // Base score for Google results
          } as RetailerResult;
        } catch (err) {
          console.warn(
            `Error processing Google Place ${suggestion.placePrediction.placeId}:`,
            err
          );
          // Log more details for debugging
          console.warn(
            `Place details: ${JSON.stringify({
              placeId: suggestion.placePrediction.placeId,
              mainText:
                suggestion.placePrediction.structuredFormat?.mainText?.text,
              secondaryText:
                suggestion.placePrediction.structuredFormat?.secondaryText
                  ?.text,
            })}`
          );
          return null;
        }
      })
    );

    // Filter out any null results from errors
    return googleResults.filter(
      (result) => result !== null
    ) as RetailerResult[];
  } catch (error) {
    console.error("Error searching Google Places:", error);
    return [];
  }
}

/**
 * Search for retailers in our database and Google Places
 * @param searchQuery The search query to look for retailers
 * @param searchByLocation If true, will search by location (city, state, zip)
 * @param includeProductCount If true, will count products for each retailer (slower)
 * @returns Array of retailer results from both sources, sorted by relevance
 */
export const searchRetailers = async (
  searchQuery: string,
  searchByLocation: boolean = false,
  includeProductCount: boolean = true
): Promise<RetailerResult[]> => {
  console.warn(
    "MIGRATION: searchRetailers is now using Supabase. Consider using searchRetailersSupabase directly."
  );

  // Use the Supabase implementation instead of Firebase
  try {
    // First, try the Supabase implementation
    const supabaseResults = await searchRetailersSupabase(
      searchQuery,
      includeProductCount
    );

    // If we got results from Supabase, return them
    if (supabaseResults.length > 0) {
      return supabaseResults;
    }

    // If no results from Supabase, fallback to Google Places
    const googleResults = await searchRetailersInGooglePlaces(searchQuery);
    return googleResults;
  } catch (error) {
    console.error("Error in searchRetailers:", error);

    // Fallback to Google Places on error
    try {
      return await searchRetailersInGooglePlaces(searchQuery);
    } catch (googleError) {
      console.error("Error in Google Places fallback:", googleError);
      return [];
    }
  }
};

/**
 * Merge and dedup results from database and Google
 */
export function mergeDedupResults(
  databaseResults: RetailerResult[],
  googleResults: RetailerResult[]
): RetailerResult[] {
  // If we have no database results, just return Google results
  if (databaseResults.length === 0) return googleResults;

  // If we have no Google results, just return database results
  if (googleResults.length === 0) return databaseResults;

  // Add all database results to our final list
  const combinedResults: RetailerResult[] = [...databaseResults];

  // For each Google result, check if it might be a duplicate of a DB result
  for (const googleResult of googleResults) {
    // Check if we already have this result (by comparing names)
    const googleName = googleResult.dispensary_name.toLowerCase();

    const isDuplicate = databaseResults.some((dbResult) => {
      const dbName = dbResult.dispensary_name.toLowerCase();

      // Simple name comparison - could be enhanced with fuzzy matching
      return (
        dbName === googleName ||
        dbName.includes(googleName) ||
        googleName.includes(dbName)
      );
    });

    // If it's not a duplicate, add it
    if (!isDuplicate) {
      combinedResults.push(googleResult);
    }
  }

  return combinedResults;
}

/**
 * Sort results by relevance score
 */
export function sortResultsByRelevance(
  results: RetailerResult[],
  searchTerm: string
): RetailerResult[] {
  return results.sort((a, b) => {
    // Use relevance score if available
    if (a.relevanceScore !== undefined && b.relevanceScore !== undefined) {
      return b.relevanceScore - a.relevanceScore;
    }

    // Fall back to preferring database results over Google results
    if (a.isFromOurDatabase && !b.isFromOurDatabase) return -1;
    if (!a.isFromOurDatabase && b.isFromOurDatabase) return 1;

    // For Google results (or if both from same source), prefer exact matches
    const nameA = a.dispensary_name.toLowerCase();
    const nameB = b.dispensary_name.toLowerCase();

    if (nameA === searchTerm && nameB !== searchTerm) return -1;
    if (nameB === searchTerm && nameA !== searchTerm) return 1;

    // Prefer names that start with the search term
    if (nameA.startsWith(searchTerm) && !nameB.startsWith(searchTerm))
      return -1;
    if (nameB.startsWith(searchTerm) && !nameA.startsWith(searchTerm)) return 1;

    // Finally sort alphabetically
    return nameA.localeCompare(nameB);
  });
}

// Helper function to validate coordinates
export function validateCoordinates(lat: number, lng: number): boolean {
  return (
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180 &&
    lat !== 0 &&
    lng !== 0
  );
}

// Uses Supabase backend instead of Firebase for nearby search
export async function searchNearbyRetailersSupabase(
  latitude: number,
  longitude: number,
  radiusMiles: number = 30,
  page: number = 1,
  pageSize: number = 4,
  preferredState?: string
): Promise<RetailerResult[]> {
  try {
    // Validate input coordinates
    if (!validateCoordinates(latitude, longitude)) {
      console.error(
        `Invalid coordinates for nearby search: lat=${latitude}, lng=${longitude}`
      );
      throw new Error("Invalid coordinates provided for nearby search");
    }

    // Build URL with optional preferredState parameter
    const params = new URLSearchParams({
      latitude: latitude.toString(),
      longitude: longitude.toString(),
      radius: radiusMiles.toString(),
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    if (preferredState) {
      params.append("preferredState", preferredState);
    }

    const url = `/api/misc/retailers/nearby?${params.toString()}`;

    console.log(`🔍 COMPETITOR SEARCH API CALL:`, {
      latitude,
      longitude,
      radiusMiles,
      page,
      pageSize,
      preferredState,
      url,
    });

    // Use fetch directly to avoid API client type issues, using misc controller
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem("auth_token") || ""}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `Nearby retailers API error: ${response.status} - ${errorText}`
      );
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.retailers) {
      console.log(`Found ${data.retailers.length} nearby retailers`);

      // Map Supabase retailer format to RetailerResult format
      const retailers = data.retailers.map((retailer: any) => {
        const lat = parseFloat(retailer.latitude) || 0;
        const lng = parseFloat(retailer.longitude) || 0;

        // Log coordinate validation for each retailer
        if (!validateCoordinates(lat, lng)) {
          console.warn(
            `Invalid coordinates for retailer ${retailer.name}: lat=${lat}, lng=${lng}`
          );
        }

        return {
          id: retailer.retailer_id,
          dispensary_name: retailer.name || retailer.dispensary_name,
          physical_address: retailer.address || retailer.physical_address,
          city: retailer.city,
          state: retailer.state,
          zip_code: retailer.zip_code,
          country: retailer.country || "US",
          contact_phone: retailer.phone || retailer.contact_phone,
          contact_email: retailer.email || retailer.contact_email,
          website_url: retailer.website_url,
          latitude: lat,
          longitude: lng,
          distance: retailer.distance || 0,
          productCount: retailer.product_count || 0,
          isFromOurDatabase: true,
        };
      });

      console.log(
        `Processed ${retailers.length} nearby retailers with valid data`
      );
      return retailers;
    }

    // Fallback to empty array if no results
    return [];
  } catch (error) {
    console.error("Error searching nearby retailers:", error);
    return [];
  }
}

/**
 * Search for competitor retailers based on a location (to find nearby dispensaries)
 * @param latitude Location latitude
 * @param longitude Location longitude
 * @param radius Search radius in miles (default: 10)
 * @param includeProductCount If true, will count products for each retailer (slower)
 * @returns Array of retailer results
 */
export const searchNearbyRetailers = async (
  latitude: number,
  longitude: number,
  radius: number = 10,
  includeProductCount: boolean = true,
  page: number = 1,
  pageSize: number = 4
): Promise<RetailerResult[]> => {
  console.warn(
    "MIGRATION: searchNearbyRetailers is now using Supabase. Consider using searchNearbyRetailersSupabase directly."
  );

  // Use the Supabase implementation
  return searchNearbyRetailersSupabase(
    latitude,
    longitude,
    radius,
    page,
    pageSize
  );
};

// Helper function to calculate distance between two points in miles
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 3958.8; // Earth radius in miles
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) *
      Math.cos(toRad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return Math.round(distance * 10) / 10; // Round to 1 decimal place
}

function toRad(degrees: number): number {
  return (degrees * Math.PI) / 180;
}
