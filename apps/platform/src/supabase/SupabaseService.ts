import { createClient, SupabaseClient } from "@supabase/supabase-js";
import axios from "axios";
import { logger } from "../config/logger";

interface SupabaseConfig {
  url: string;
  key: string;
  bucket?: string;
  timeout?: number; // Query timeout in milliseconds
}

interface SupabaseTableResult {
  tableName: string;
  recordCount: number;
}

interface SupabaseIntegrationStatus {
  isConnected: boolean;
  lastSyncTime?: string;
  totalRecords?: number;
  tables?: {
    name: string;
    recordCount: number;
    lastUpdated: string;
  }[];
}

// Data sources structure
interface DataSources {
  pos?: any;
  competitors?: any[] | null;
  social_media?: any[] | null;
  documents?: any[] | null;
}

// Type definition for nearby retailer result
interface NearbyRetailerResult {
  retailer_id: string;
  distance: number;
}

export class SupabaseService {
  private client: SupabaseClient;
  private config: SupabaseConfig;
  private bucket: string;

  // Renamed getter to avoid duplicate identifier
  get supabaseClient(): SupabaseClient {
    return this.client;
  }

  constructor(config: SupabaseConfig) {
    this.config = config;

    // Create client with timeout configuration
    const clientOptions: any = {};
    if (config.timeout) {
      clientOptions.db = {
        schema: "public",
        // Set statement timeout for long-running queries
        options: `statement_timeout=${config.timeout}ms`,
      };
    }

    this.client = createClient(config.url, config.key, clientOptions);
    this.bucket = config.bucket || "location-data";

    // Initialize bucket
    this.initializeStorage();
  }

  /**
   * Initialize Supabase storage bucket
   */
  private async initializeStorage(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets } = await this.client.storage.listBuckets();
      const bucketExists = buckets?.some(
        (bucket) => bucket.name === this.bucket
      );

      if (!bucketExists) {
        // Create the bucket
        await this.client.storage.createBucket(this.bucket, {
          public: true,
        });
      }
    } catch (error) {
      console.error("Error initializing Supabase storage:", error);
    }
  }

  /**
   * Upload location data to Supabase
   */
  async uploadLocationData(
    locationId: number | string,
    dataSources: DataSources
  ): Promise<SupabaseTableResult> {
    try {
      // Table name for this location's data
      const tableName = `location_${locationId}_data`;

      // Transform data for Supabase format
      const formattedData = this.formatDataForSupabase(locationId, dataSources);

      // Insert data into Supabase table
      const { error } = await this.client.from(tableName).upsert(formattedData);

      if (error) {
        if (error.code === "42P01") {
          // Table doesn't exist, create it
          await this.createTable(tableName);

          // Try inserting again
          const { error: insertError } = await this.client
            .from(tableName)
            .upsert(formattedData);

          if (insertError) {
            throw new Error(`Failed to insert data: ${insertError.message}`);
          }
        } else {
          throw new Error(`Failed to insert data: ${error.message}`);
        }
      }

      return {
        tableName,
        recordCount: formattedData.length,
      };
    } catch (error: any) {
      console.error("Error uploading data to Supabase:", error);
      throw new Error(`Failed to upload data to Supabase: ${error.message}`);
    }
  }

  /**
   * Get Supabase integration status for a location
   */
  async getIntegrationStatus(
    locationId: number | string
  ): Promise<SupabaseIntegrationStatus> {
    try {
      // Table name for this location's data
      const tableName = `location_${locationId}_data`;

      // Check if table exists by trying to fetch a record
      const { data, error } = await this.client
        .from(tableName)
        .select("count")
        .limit(1);

      if (error) {
        // Table likely doesn't exist
        return {
          isConnected: false,
        };
      }

      // Get record count
      const { count } = await this.getTableRecordCount(tableName);

      // Get last updated time
      const { data: latestRecord } = await this.client
        .from(tableName)
        .select("created_at")
        .order("created_at", { ascending: false })
        .limit(1);

      const lastUpdated =
        latestRecord?.[0]?.created_at || new Date().toISOString();

      return {
        isConnected: true,
        lastSyncTime: lastUpdated,
        totalRecords: count,
        tables: [
          {
            name: tableName,
            recordCount: count,
            lastUpdated,
          },
        ],
      };
    } catch (error: any) {
      console.error("Error getting Supabase integration status:", error);
      throw new Error(
        `Failed to get Supabase integration status: ${error.message}`
      );
    }
  }

  /**
   * Create table for location data
   */
  private async createTable(tableName: string): Promise<void> {
    try {
      // In Supabase, we need to use SQL to create the table with the right structure
      // Using the Postgres client from Supabase
      const { error } = await this.client.rpc("create_location_table", {
        table_name: tableName,
      });

      if (error) {
        throw new Error(`Failed to create table: ${error.message}`);
      }
    } catch (error: any) {
      console.error("Error creating table:", error);
      throw new Error(`Failed to create table: ${error.message}`);
    }
  }

  /**
   * Format data for Supabase
   */
  private formatDataForSupabase(
    locationId: number | string,
    dataSources: DataSources
  ): any[] {
    const timestamp = new Date().toISOString();
    const data = [];

    // Create a record for each data source type
    if (dataSources.pos) {
      data.push({
        id: `pos_${locationId}_${Date.now()}`,
        location_id: locationId.toString(),
        timestamp,
        data_type: "pos",
        pos_data: dataSources.pos || {},
        competitors_data: null,
        social_media_data: null,
        documents_data: null,
        created_at: timestamp,
        updated_at: timestamp,
      });
    }

    // Add competitors data if it exists
    if (dataSources.competitors && dataSources.competitors.length > 0) {
      data.push({
        id: `competitors_${locationId}_${Date.now()}`,
        location_id: locationId.toString(),
        timestamp,
        data_type: "competitors",
        pos_data: null,
        competitors_data: dataSources.competitors,
        social_media_data: null,
        documents_data: null,
        created_at: timestamp,
        updated_at: timestamp,
      });
    }

    // Add social media data if it exists
    if (dataSources.social_media && dataSources.social_media.length > 0) {
      data.push({
        id: `social_media_${locationId}_${Date.now()}`,
        location_id: locationId.toString(),
        timestamp,
        data_type: "social_media",
        pos_data: null,
        competitors_data: null,
        social_media_data: dataSources.social_media,
        documents_data: null,
        created_at: timestamp,
        updated_at: timestamp,
      });
    }

    // Add documents data if it exists
    if (dataSources.documents && dataSources.documents.length > 0) {
      data.push({
        id: `documents_${locationId}_${Date.now()}`,
        location_id: locationId.toString(),
        timestamp,
        data_type: "documents",
        pos_data: null,
        competitors_data: null,
        social_media_data: null,
        documents_data: dataSources.documents,
        created_at: timestamp,
        updated_at: timestamp,
      });
    }

    return data;
  }

  /**
   * Get record count for a table
   */
  private async getTableRecordCount(
    tableName: string
  ): Promise<{ count: number }> {
    try {
      const { data, error, count } = await this.client
        .from(tableName)
        .select("*", { count: "exact", head: true });

      if (error) {
        throw new Error(`Error counting records: ${error.message}`);
      }

      return { count: count || 0 };
    } catch (error: any) {
      console.error("Error getting table record count:", error);
      return { count: 0 };
    }
  }

  /**
   * Upload a file to Supabase storage
   */
  async uploadFile(
    locationId: number | string,
    fileBuffer: Buffer,
    fileName: string,
    contentType?: string
  ): Promise<string> {
    try {
      const filePath = `location_${locationId}/${fileName}`;

      const { data, error } = await this.client.storage
        .from(this.bucket)
        .upload(filePath, fileBuffer, {
          contentType: contentType || "application/octet-stream",
          upsert: true,
        });

      if (error) {
        throw new Error(`Failed to upload file: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = this.client.storage
        .from(this.bucket)
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error: any) {
      console.error("Error uploading file to Supabase:", error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  // Search for retailers (competitors) in Supabase
  async searchRetailers(query: string, limit: number = 10) {
    try {
      // Sanitize and normalize the query
      const cleanQuery = query.trim().toLowerCase();

      // First try with exact matching
      const { data: exactMatches, error: exactError } = await this.client
        .from("retailers")
        .select("*")
        .or(`name.ilike.%${cleanQuery}%, dispensary_name.ilike.%${cleanQuery}%`)
        .or(`address.ilike.%${cleanQuery}%, city.ilike.%${cleanQuery}%`)
        .limit(limit);

      if (exactError) throw exactError;

      // If we get exact matches, use those
      if (exactMatches && exactMatches.length > 0) {
        // Add product counts and return
        const retailersWithCounts = await this.addProductCountsToRetailers(
          exactMatches
        );
        return retailersWithCounts;
      }

      // If no exact matches, try with fuzzy search using word similarity
      // This uses Postgres's trigram similarity which works well for misspellings
      const { data: fuzzyMatches, error: fuzzyError } = await this.client.rpc(
        "search_retailers_fuzzy",
        {
          search_term: cleanQuery,
          similarity_threshold: 0.3, // Lower number = more fuzzy matches
          max_results: limit,
        }
      );

      if (fuzzyError) {
        console.warn(
          "Fuzzy search not available, falling back to basic search:",
          fuzzyError
        );
        // If RPC fails (function not set up), fall back to basic search but with split words
        return this.fallbackWordByWordSearch(cleanQuery, limit);
      }

      if (!fuzzyMatches || fuzzyMatches.length === 0) {
        // Try fallback if no fuzzy matches
        return this.fallbackWordByWordSearch(cleanQuery, limit);
      }

      // Add product counts to the fuzzy matches
      const retailersWithCounts = await this.addProductCountsToRetailers(
        fuzzyMatches
      );
      return retailersWithCounts;
    } catch (error) {
      console.error("Error searching retailers in Supabase:", error);
      // Try the fallback search as a last resort
      try {
        return this.fallbackWordByWordSearch(query, limit);
      } catch (fallbackError) {
        console.error("Even fallback search failed:", fallbackError);
        return [];
      }
    }
  }

  // Search for retailers by name only (for business search when >= 3 words)
  async searchRetailersByNameOnly(query: string, limit: number = 10) {
    try {
      // Sanitize and normalize the query
      const cleanQuery = query.trim().toLowerCase();

      // Search only in name and dispensary_name fields
      const { data: nameMatches, error: nameError } = await this.client
        .from("retailers")
        .select("*")
        .or(`name.ilike.%${cleanQuery}%, dispensary_name.ilike.%${cleanQuery}%`)
        .limit(limit);

      if (nameError) throw nameError;

      // If we get matches, add product counts and return
      if (nameMatches && nameMatches.length > 0) {
        const retailersWithCounts = await this.addProductCountsToRetailers(
          nameMatches
        );
        return retailersWithCounts;
      }

      // If no exact matches, try with fuzzy search on name fields only
      const { data: fuzzyMatches, error: fuzzyError } = await this.client.rpc(
        "search_retailers_fuzzy_name_only",
        {
          search_term: cleanQuery,
          similarity_threshold: 0.3,
          max_results: limit,
        }
      );

      if (fuzzyError) {
        console.warn(
          "Fuzzy name search not available, returning empty results:",
          fuzzyError
        );
        return [];
      }

      if (!fuzzyMatches || fuzzyMatches.length === 0) {
        return [];
      }

      // Add product counts to the fuzzy matches
      const retailersWithCounts = await this.addProductCountsToRetailers(
        fuzzyMatches
      );
      return retailersWithCounts;
    } catch (error) {
      console.error("Error searching retailers by name:", error);
      throw error;
    }
  }

  // Search for retailers by city or state (for business search when < 3 words)
  async searchRetailersByCityState(query: string, limit: number = 10) {
    try {
      // Sanitize and normalize the query
      const cleanQuery = query.trim().toLowerCase();

      // Search only in city and state fields
      const { data: locationMatches, error: locationError } = await this.client
        .from("retailers")
        .select("*")
        .or(`city.ilike.%${cleanQuery}%, state.ilike.%${cleanQuery}%`)
        .limit(limit);

      if (locationError) throw locationError;

      // If we get matches, add product counts and return
      if (locationMatches && locationMatches.length > 0) {
        const retailersWithCounts = await this.addProductCountsToRetailers(
          locationMatches
        );
        return retailersWithCounts;
      }

      return [];
    } catch (error) {
      console.error("Error searching retailers by city/state:", error);
      throw error;
    }
  }

  // Helper method to add product counts to retailer results
  private async addProductCountsToRetailers(retailers: any[]) {
    if (!retailers || retailers.length === 0) {
      return [];
    }

    // For each retailer, get a product count
    const retailersWithCounts = await Promise.all(
      retailers.map(async (retailer) => {
        try {
          // Get product count for this retailer
          const { data, error, count } = await this.client
            .from("products")
            .select("*", { count: "exact", head: true })
            .eq("retailer_id", retailer.retailer_id);
          // Add the product count to the retailer data
          return {
            ...retailer,
            product_count: count || 0,
          };
        } catch (countError) {
          console.error(
            `Error getting product count for retailer ${retailer.retailer_id}:`,
            countError
          );
          // Return the retailer without a product count if there was an error
          return {
            ...retailer,
            product_count: 0,
          };
        }
      })
    );

    return retailersWithCounts || [];
  }

  // Fallback search method that splits query into words and searches for each
  private async fallbackWordByWordSearch(query: string, limit: number) {
    // Split the query into individual words
    const words = query.split(/\s+/).filter((word) => word.length > 2);

    if (words.length === 0) {
      return [];
    }

    // Search for each word individually
    const results: any[] = [];
    for (const word of words) {
      const { data: wordMatches, error } = await this.client
        .from("retailers")
        .select("*")
        .or(`name.ilike.%${word}%, dispensary_name.ilike.%${word}%`)
        .or(`address.ilike.%${word}%, city.ilike.%${word}%`)
        .limit(limit);

      if (!error && wordMatches && wordMatches.length > 0) {
        // Add results, avoiding duplicates
        for (const match of wordMatches) {
          if (!results.some((r) => r.retailer_id === match.retailer_id)) {
            results.push(match);
          }

          // Stop if we have enough results
          if (results.length >= limit) {
            break;
          }
        }
      }

      // Stop if we have enough results
      if (results.length >= limit) {
        break;
      }
    }

    // Add product counts and return
    return await this.addProductCountsToRetailers(results);
  }

  // Search for retailers by location (city, state, zip)
  async searchRetailersByLocation(
    city?: string,
    state?: string,
    zip?: string,
    radius: number = 30,
    limit: number = 20
  ) {
    try {
      console.log(
        `🏙️ SupabaseService: Searching retailers by location: city="${city}", state="${state}", zip="${zip}"`
      );

      // Build the query based on available location parameters
      const queryBuilder = this.client.from("retailers").select("*");

      // Apply location filters using flexible matching logic:
      // Priority: (city AND state) OR (city AND zip) OR (state AND zip)
      // We don't rely on city alone, but state and/or zip are reliable

      console.log(
        `🔍 Searching with city="${city}", state="${state}", zip="${zip}"`
      );

      const searchQueries = [];

      // Query 1: City AND State (most common and reliable)
      if (city && state) {
        searchQueries.push({
          name: "city+state",
          query: this.client
            .from("retailers")
            .select("*")
            .ilike("city", `%${city.trim()}%`)
            .ilike("state", `%${state.trim()}%`)
            .limit(limit),
        });
      }

      // Query 2: City AND Zip (when state might be abbreviated differently)
      if (city && zip) {
        searchQueries.push({
          name: "city+zip",
          query: this.client
            .from("retailers")
            .select("*")
            .ilike("city", `%${city.trim()}%`)
            .not("zip_code", "is", null)
            .ilike("zip_code", `%${zip.trim()}%`)
            .limit(limit),
        });
      }

      // Query 3: State AND Zip (when city might be named differently)
      if (state && zip) {
        searchQueries.push({
          name: "state+zip",
          query: this.client
            .from("retailers")
            .select("*")
            .ilike("state", `%${state.trim()}%`)
            .not("zip_code", "is", null)
            .ilike("zip_code", `%${zip.trim()}%`)
            .limit(limit),
        });
      }

      // Query 4: State only (fallback when only state is provided)
      if (state && !city && !zip) {
        searchQueries.push({
          name: "state-only",
          query: this.client
            .from("retailers")
            .select("*")
            .ilike("state", `%${state.trim()}%`)
            .limit(limit),
        });
      }

      // Query 5: Zip only (fallback when only zip is provided)
      if (zip && !city && !state) {
        searchQueries.push({
          name: "zip-only",
          query: this.client
            .from("retailers")
            .select("*")
            .not("zip_code", "is", null)
            .ilike("zip_code", `%${zip.trim()}%`)
            .limit(limit),
        });
      }

      // Execute queries in priority order and return first successful result
      for (const searchQuery of searchQueries) {
        try {
          console.log(`🔍 Trying ${searchQuery.name} search...`);
          const { data, error } = await searchQuery.query;

          if (!error && data && data.length > 0) {
            console.log(
              `✅ Found ${data.length} retailers using ${searchQuery.name} search`
            );
            const retailersWithCounts = await this.addProductCountsToRetailers(
              data
            );
            return this.transformRetailers(retailersWithCounts);
          } else if (error) {
            console.warn(`⚠️ ${searchQuery.name} search failed:`, error);
          } else {
            console.log(`📍 ${searchQuery.name} search returned no results`);
          }
        } catch (error) {
          console.error(`❌ Error in ${searchQuery.name} search:`, error);
          // Continue to next query
        }
      }

      // If we reach here, no queries returned results
      console.log(`📍 No retailers found for any search combination`);
      return [];
    } catch (error) {
      console.error(
        "Error searching retailers by location in Supabase:",
        error
      );
      throw error;
    }
  }

  // Helper method to transform retailers to expected format
  private transformRetailers(retailers: any[]) {
    return retailers.map((retailer: any) => ({
      ...retailer,
      dispensary_name: retailer.dispensary_name || retailer.name,
      physical_address: retailer.physical_address || retailer.address,
      contact_phone: retailer.contact_phone || retailer.phone,
      website_url: retailer.website_url || retailer.website,
      contact_email: retailer.contact_email || retailer.email,
      zip_code: retailer.zip_code || retailer.zip,
      latitude: parseFloat(retailer.latitude) || 0,
      longitude: parseFloat(retailer.longitude) || 0,
      distance: 0, // No distance calculation for location-based search
      isFromOurDatabase: true,
    }));
  }

  // Search for retailers near a specific location
  async searchNearbyRetailers(
    latitude: number,
    longitude: number,
    radius: number = 15,
    limit: number = 10,
    preferredState?: string
  ) {
    try {
      // Use PostGIS to find retailer IDs and distances within radius
      const { data: nearbyData, error: nearbyError } = await this.client.rpc(
        "find_retailer_ids_within_radius",
        {
          lat: latitude,
          lng: longitude,
          radius_miles: radius,
          max_results: limit * 2, // Fetch more to ensure we have enough after filtering
        }
      );

      if (nearbyError) {
        console.error(
          "🚨 PostGIS function 'find_retailer_ids_within_radius' not available, falling back to manual calculation:",
          nearbyError
        );
        console.error("Error details:", {
          message: nearbyError.message,
          code: nearbyError.code,
          details: nearbyError.details,
        });
        return this.fallbackNearbySearch(
          latitude,
          longitude,
          radius,
          limit * 2,
          preferredState
        );
      }

      if (!nearbyData || nearbyData.length === 0) {
        console.log("🔍 PostGIS function returned no nearby retailers");
        return [];
      }

      console.log(`✅ PostGIS function found ${nearbyData.length} nearby retailers`);
      console.log("Sample nearby data:", nearbyData.slice(0, 2));

      // Get the retailer IDs
      const retailerIds = nearbyData.map(
        (item: NearbyRetailerResult) => item.retailer_id
      );

      // Fetch the full retailer details using the IDs
      const retailerQuery = this.client
        .from("retailers")
        .select("*")
        .in("retailer_id", retailerIds);

      // If we have a preferred state, prioritize retailers from that state
      // but don't exclude others completely (in case there are no retailers in the preferred state)
      const { data: retailers, error: retailersError } = await retailerQuery;

      if (retailersError || !retailers) {
        console.error("Error fetching retailer details:", retailersError);
        return this.fallbackNearbySearch(
          latitude,
          longitude,
          radius,
          limit * 2,
          preferredState
        );
      }

      // Add distance information from the nearbyData
      const retailersWithDistance = retailers
        .map((retailer) => {
          const distanceInfo = nearbyData.find(
            (item: NearbyRetailerResult) =>
              item.retailer_id === retailer.retailer_id
          );
          return {
            ...retailer,
            distance: distanceInfo ? distanceInfo.distance : null,
          };
        });

      // If we have a preferred state, filter to only show retailers from that state first
      let filteredRetailers = retailersWithDistance;
      if (preferredState) {
        console.log(
          `🔍 Filtering ${retailersWithDistance.length} retailers by preferred state "${preferredState}"`
        );

        // Log all retailer states for debugging
        const allStates = retailersWithDistance.map(r => r.state).filter(Boolean);
        const uniqueStates = [...new Set(allStates)];
        console.log(`📍 Available retailer states: ${uniqueStates.join(', ')}`);

        const sameStateRetailers = retailersWithDistance.filter(
          (retailer) => {
            const retailerState = retailer.state?.trim().toLowerCase();
            const preferredStateLower = preferredState.trim().toLowerCase();

            // Check for exact match first
            let matches = retailerState === preferredStateLower;

            // If no exact match, try common state abbreviations
            if (!matches && retailerState && preferredStateLower) {
              const stateAbbreviations: Record<string, string[]> = {
                california: ['ca', 'calif'],
                ca: ['california', 'calif'],
                illinois: ['il', 'ill'],
                il: ['illinois', 'ill'],
                'new york': ['ny'],
                ny: ['new york'],
                texas: ['tx', 'tex'],
                tx: ['texas', 'tex'],
                florida: ['fl', 'fla'],
                fl: ['florida', 'fla'],
              };

              const possibleMatches = stateAbbreviations[preferredStateLower] || [];
              matches = possibleMatches.includes(retailerState) ||
                       (stateAbbreviations[retailerState] || []).includes(preferredStateLower);
            }

            if (!matches && retailer.state) {
              console.log(`❌ Filtering out retailer "${retailer.dispensary_name || retailer.name}" from state "${retailer.state}" (preferred: "${preferredState}")`);
            } else if (matches) {
              console.log(`✅ Keeping retailer "${retailer.dispensary_name || retailer.name}" from state "${retailer.state}"`);
            }
            return matches;
          }
        );

        // If we found retailers in the same state, use only those
        // Otherwise, fall back to all retailers (for edge cases where no same-state retailers exist)
        if (sameStateRetailers.length > 0) {
          console.log(
            `🎯 Found ${sameStateRetailers.length} retailers in preferred state "${preferredState}", filtering out other states`
          );
          filteredRetailers = sameStateRetailers;
        } else {
          console.log(
            `⚠️ No retailers found in preferred state "${preferredState}", showing all nearby retailers`
          );
        }
      }

      // Sort the filtered results by distance
      const sortedRetailers = filteredRetailers.sort((a, b) => {
        return (a.distance || 999) - (b.distance || 999);
      });

      // Add product counts to the filtered and sorted retailers
      const retailersWithCounts = await this.addProductCountsToRetailers(
        sortedRetailers.slice(0, limit)
      );

      // Identify retailers with 0 products and call the scraper
      try {
        const zeroProductRetailers = retailersWithCounts.filter(
          (retailer) => retailer.product_count === 0
        );

        if (zeroProductRetailers.length > 0) {
          // Group retailers by state for efficient scraping
          const retailersByState = zeroProductRetailers.reduce<
            Record<string, string[]>
          >((acc, retailer) => {
            if (!retailer.state) return acc;

            if (!acc[retailer.state]) {
              acc[retailer.state] = [];
            }
            acc[retailer.state].push(retailer.retailer_id);
            return acc;
          }, {});

          // Call scraper for each state group
          for (const [state, retailerIds] of Object.entries(retailersByState)) {
            await axios.post(
              "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1/scrape-multiple-retailers?source=cannmenus",
              {
                retailer_ids: retailerIds,
                states: [state],
              }
            );

            logger.info(
              `Triggered scraper for ${retailerIds.length} retailers in ${state}`
            );
          }
        }
      } catch (scraperError) {
        logger.error(
          "Error calling scraper API for nearby retailers:",
          scraperError
        );
        // Don't fail the overall request if scraper call fails
      }

      return retailersWithCounts;
    } catch (error) {
      console.error("Error searching nearby retailers in Supabase:", error);
      // Fallback to manual calculation
      return this.fallbackNearbySearch(
        latitude,
        longitude,
        radius,
        limit * 2,
        preferredState
      );
    }
  }

  // Fallback method when PostGIS functions aren't available
  private async fallbackNearbySearch(
    latitude: number,
    longitude: number,
    radius: number = 15,
    limit: number = 20,
    preferredState?: string
  ) {
    try {
      // Get all retailers and filter manually
      // This is less efficient but works as a fallback
      const { data, error } = await this.client
        .from("retailers")
        .select("*")
        .limit(limit * 3); // Get more to account for filtering

      if (error) throw error;

      if (!data || data.length === 0) {
        return [];
      }

      // Calculate distances and filter
      const retailersWithDistance = data
        .map((retailer) => {
          const retailerLat = parseFloat(retailer.latitude || "0");
          const retailerLng = parseFloat(retailer.longitude || "0");

          // Calculate distance using Haversine formula
          const distance = this.calculateDistance(
            latitude,
            longitude,
            retailerLat,
            retailerLng
          );

          return {
            ...retailer,
            distance,
          };
        })
        .filter((retailer) => retailer.distance <= radius);

      // Apply state filtering if preferred state is provided
      let filteredRetailers = retailersWithDistance;
      if (preferredState) {
        const sameStateRetailers = retailersWithDistance.filter(
          (retailer) => {
            const retailerState = retailer.state?.trim().toLowerCase();
            const preferredStateLower = preferredState.trim().toLowerCase();

            // Check for exact match first
            let matches = retailerState === preferredStateLower;

            // If no exact match, try common state abbreviations
            if (!matches && retailerState && preferredStateLower) {
              const stateAbbreviations: Record<string, string[]> = {
                california: ['ca', 'calif'],
                ca: ['california', 'calif'],
                illinois: ['il', 'ill'],
                il: ['illinois', 'ill'],
                'new york': ['ny'],
                ny: ['new york'],
                texas: ['tx', 'tex'],
                tx: ['texas', 'tex'],
                florida: ['fl', 'fla'],
                fl: ['florida', 'fla'],
              };

              const possibleMatches = stateAbbreviations[preferredStateLower] || [];
              matches = possibleMatches.includes(retailerState) ||
                       (stateAbbreviations[retailerState] || []).includes(preferredStateLower);
            }

            return matches;
          }
        );

        // If we found retailers in the same state, use only those
        if (sameStateRetailers.length > 0) {
          console.log(
            `🎯 Fallback: Found ${sameStateRetailers.length} retailers in preferred state "${preferredState}", filtering out other states`
          );
          filteredRetailers = sameStateRetailers;
        } else {
          console.log(
            `⚠️ Fallback: No retailers found in preferred state "${preferredState}", showing all nearby retailers`
          );
        }
      }

      // Sort by distance and limit results
      const sortedRetailers = filteredRetailers
        .sort((a, b) => a.distance - b.distance)
        .slice(0, limit);

      if (sortedRetailers.length === 0) {
        return [];
      }

      // Add product counts
      const retailersWithCounts = await this.addProductCountsToRetailers(
        sortedRetailers
      );

      // Identify retailers with 0 products and call the scraper
      try {
        const zeroProductRetailers = retailersWithCounts.filter(
          (retailer) => retailer.product_count === 0
        );

        if (zeroProductRetailers.length > 0) {
          // Group retailers by state for efficient scraping
          const retailersByState = zeroProductRetailers.reduce<
            Record<string, string[]>
          >((acc, retailer) => {
            if (!retailer.state) return acc;

            if (!acc[retailer.state]) {
              acc[retailer.state] = [];
            }
            acc[retailer.state].push(retailer.retailer_id);
            return acc;
          }, {});

          // Call scraper for each state group
          for (const [state, retailerIds] of Object.entries(retailersByState)) {
            await axios.post(
              "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1/scrape-multiple-retailers?source=cannmenus",
              {
                retailer_ids: retailerIds,
                states: [state],
              }
            );

            logger.info(
              `Triggered scraper for ${retailerIds.length} retailers in ${state}`
            );
          }
        }
      } catch (scraperError) {
        logger.error(
          "Error calling scraper API for nearby retailers in fallback search:",
          scraperError
        );
        // Don't fail the overall request if scraper call fails
      }

      return retailersWithCounts;
    } catch (error) {
      console.error("Error in fallback nearby retailers search:", error);
      return [];
    }
  }

  // Get retailer by ID
  async getRetailerById(retailerId: string) {
    try {
      const { data, error } = await this.client
        .from("retailers")
        .select("*")
        .eq("retailer_id", retailerId)
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error("Error getting retailer by ID from Supabase:", error);
      throw error;
    }
  }

  // Get retailer products with optimized query and timeout handling
  async getRetailerProducts(retailerId: string, limit: number = 5000) {
    const startTime = Date.now();

    try {
      if (!retailerId) {
        throw new Error("Retailer ID is required for getRetailerProducts");
      }

      // Log the request with performance tracking
      logger.info({
        message: "Retrieving retailer products from Supabase",
        retailerId,
        limit,
        query: `SELECT * FROM products WHERE retailer_id = '${retailerId}' LIMIT ${limit}`,
        timestamp: new Date().toISOString(),
      });

      // First, try to get just the count to check if we have a reasonable dataset size
      const { count: totalCount, error: countError } = await this.client
        .from("products")
        .select("*", { count: "exact", head: true })
        .eq("retailer_id", retailerId);

      if (countError) {
        logger.error({
          message: "Error getting product count for retailer",
          retailerId,
          error: countError,
          code: countError.code,
          details: countError.details,
        });
        throw countError;
      }

      logger.info({
        message: "Product count retrieved",
        retailerId,
        totalCount,
        queryTime: Date.now() - startTime,
      });

      // If we have too many products, use pagination to avoid timeouts
      if (totalCount && totalCount > 10000) {
        logger.warn({
          message: "Large dataset detected, using pagination",
          retailerId,
          totalCount,
          requestedLimit: limit,
        });

        // For very large datasets, limit the response and suggest pagination
        const adjustedLimit = Math.min(limit, 1000);
        const { data, error } = await this.client
          .from("products")
          .select("*")
          .eq("retailer_id", retailerId)
          .order("updated_at", { ascending: false }) // Get most recent products first
          .limit(adjustedLimit);

        if (error) {
          throw error;
        }

        return {
          products: data || [],
          total_count: totalCount,
          is_paginated: true,
          returned_count: data?.length || 0,
          message: `Returned ${adjustedLimit} most recent products out of ${totalCount} total. Use pagination for complete dataset.`,
        };
      }

      // For reasonable dataset sizes, get the full data
      const { data, error } = await this.client
        .from("products")
        .select("*")
        .eq("retailer_id", retailerId)
        .limit(limit);

      // Log the raw response
      logger.info({
        message: "Raw Supabase response",
        retailerId,
        hasData: Boolean(data),
        dataLength: data?.length || 0,
        count: totalCount,
        error: error
          ? {
              message: error.message,
              code: error.code,
              details: error.details,
            }
          : null,
      });

      if (error) {
        const queryTime = Date.now() - startTime;
        logger.error({
          message: "Supabase error getting retailer products",
          retailerId,
          error: error.message,
          code: error.code,
          details: error.details,
          queryTime,
          isTimeout:
            error.code === "57014" || error.message?.includes("timeout"),
        });

        // For timeout errors, provide specific guidance
        if (error.code === "57014" || error.message?.includes("timeout")) {
          logger.error({
            message: "Database query timeout detected",
            retailerId,
            suggestedActions: [
              "Check if retailer_id index exists on products table",
              "Consider reducing query limit",
              "Use pagination for large datasets",
              "Check database performance metrics",
            ],
            queryTime,
          });
        }

        throw error;
      }

      // Log sample of products if available
      if (data && data.length > 0) {
        logger.info({
          message: "Sample of retrieved products",
          retailerId,
          sampleProducts: data.slice(0, 3).map((p) => ({
            id: p.id,
            name: p.product_name,
            price: p.latest_price,
            category: p.category,
          })),
        });
      }

      const queryTime = Date.now() - startTime;
      logger.info({
        message: "Successfully retrieved retailer products",
        retailerId,
        productCount: data.length,
        totalCount,
        queryTime,
        performanceStatus:
          queryTime > 5000 ? "SLOW" : queryTime > 2000 ? "MODERATE" : "FAST",
      });

      // Return both the products and the total count
      return {
        products: data || [],
        total_count: totalCount || 0,
        query_time_ms: queryTime,
      };
    } catch (error) {
      const queryTime = Date.now() - startTime;

      logger.error({
        message: "Error getting retailer products from Supabase",
        retailerId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        queryTime,
        errorType:
          error instanceof Error ? error.constructor.name : typeof error,
        isTimeout:
          error instanceof Error &&
          (error.message?.includes("timeout") ||
            error.message?.includes("57014") ||
            error.message?.includes("canceling statement")),
      });

      // Return empty result instead of throwing to prevent cascading failures
      return {
        products: [],
        total_count: 0,
        error: error instanceof Error ? error.message : String(error),
        query_time_ms: queryTime,
      };
    }
  }

  // Helper method to calculate distance between two points using Haversine formula
  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ) {
    const R = 3958.8; // Earth's radius in miles
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c;
    return d;
  }

  private toRadians(degrees: number) {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get only the count of products for a retailer without fetching all products
   */
  async getRetailerProductCount(
    retailerId: string
  ): Promise<{ count: number }> {
    try {
      const { count, error } = await this.client
        .from("products")
        .select("count", { count: "exact", head: true })
        .eq("retailer_id", retailerId);

      if (error) {
        throw error;
      }

      return { count: count || 0 };
    } catch (error) {
      logger.error(
        `Error getting product count for retailer ${retailerId}:`,
        error
      );
      return { count: 0 };
    }
  }

  /**
   * Get market snapshot data with enhanced robustness, caching, and retry logic
   */
  async getMarketSnapshotData(
    competitorPlaceIds: string[],
    userRetailerId?: string
  ): Promise<any> {
    const maxRetries = 2; // Reduced retries for faster failure
    const baseDelay = 500; // Shorter delay
    const timeoutMs = 15000; // 15 second timeout

    // Input validation
    if (!competitorPlaceIds || competitorPlaceIds.length === 0) {
      logger.warn("No competitor place IDs provided for market analysis");
      return [];
    }

    // Remove duplicates and invalid IDs, limit to 5 competitors for performance
    const cleanCompetitorIds = [...new Set(competitorPlaceIds)]
      .filter((id) => id && typeof id === "string" && id.trim().length > 0)
      .map((id) => id.trim())
      .slice(0, 5); // Limit to 5 competitors to prevent timeouts

    if (cleanCompetitorIds.length === 0) {
      logger.warn("No valid competitor place IDs after cleaning");
      return [];
    }

    // Log the request for debugging
    logger.info("Market snapshot data request:", {
      competitorCount: cleanCompetitorIds.length,
      userRetailerId: userRetailerId || "none",
      cleanCompetitorIds: cleanCompetitorIds.slice(0, 3), // Log first 3 for debugging
    });

    // First, quickly check data availability
    try {
      const { data: availability, error: availabilityError } =
        await Promise.race([
          this.client.rpc("check_market_data_availability", {
            competitor_place_ids: cleanCompetitorIds,
            user_retailer_id: userRetailerId || null,
          }),
          new Promise<never>((_resolve, reject) =>
            setTimeout(
              () => reject(new Error("Availability check timeout")),
              5000
            )
          ),
        ]);

      if (availabilityError) {
        logger.warn("Data availability check failed:", availabilityError);
      } else if (availability && availability[0]) {
        const stats = availability[0];
        logger.info("Data availability check:", stats);

        if (
          stats.competitor_products_available === 0 &&
          stats.user_products_available === 0
        ) {
          logger.warn("No market data available for any competitors");
          return [];
        }
      }
    } catch (error) {
      logger.warn("Data availability check failed, proceeding anyway:", error);
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Use exponential backoff for retries
        if (attempt > 1) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          logger.info(
            `Retrying market analysis (attempt ${attempt}/${maxRetries}) after ${delay}ms delay`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        const startTime = Date.now();

        // Add timeout to the query
        const { data, error } = await Promise.race([
          this.client.rpc("get_market_snapshot_data", {
            competitor_place_ids: cleanCompetitorIds,
            user_retailer_id: userRetailerId || null,
          }),
          new Promise<never>((_resolve, reject) =>
            setTimeout(
              () => reject(new Error("Query timeout after 15 seconds")),
              timeoutMs
            )
          ),
        ]);

        const duration = Date.now() - startTime;

        if (error) {
          throw new Error(`Supabase RPC error: ${error.message}`);
        }

        logger.info("Market snapshot data retrieved successfully:", {
          duration: `${duration}ms`,
          recordCount: data?.length || 0,
          attempt,
        });

        return data || [];
      } catch (error: any) {
        lastError = error;
        logger.warn(`Market analysis attempt ${attempt} failed:`, {
          error: error.message,
          attempt,
          competitorCount: cleanCompetitorIds.length,
        });

        // Check for timeout-related errors
        if (
          error.message?.includes("timeout") ||
          error.message?.includes("Query timeout") ||
          error.message?.includes("upstream timeout") ||
          error.message?.includes("connection")
        ) {
          // For timeout errors, reduce competitor count and try again
          if (attempt === 1 && cleanCompetitorIds.length > 2) {
            cleanCompetitorIds.splice(2); // Keep only first 2 competitors
            logger.info(
              `Reducing to ${cleanCompetitorIds.length} competitors due to timeout`
            );
            continue;
          }
        } else if (
          error.message?.includes("function") &&
          error.message?.includes("does not exist")
        ) {
          // Function doesn't exist - don't retry
          break;
        }
      }
    }

    // If all retries failed, return empty result instead of throwing
    logger.error(
      "Market analysis failed after all retries, returning empty result:",
      {
        error: lastError?.message,
        competitorCount: cleanCompetitorIds.length,
        maxRetries,
      }
    );

    // Return empty result instead of throwing to prevent UI crashes
    return [];
  }

  /**
   * Get regional market data by city and/or state
   */
  async getRegionalMarketDataByLocation(
    city?: string,
    state?: string,
    userRetailerId?: string,
    maxRetailers: number = 50
  ): Promise<any> {
    const maxRetries = 3;
    const baseDelay = 1000;

    // Input validation
    if (!city && !state) {
      throw new Error(
        "Either city or state must be provided for regional analysis"
      );
    }

    // Clean inputs
    const cleanCity = city?.trim() || null;
    const cleanState = state?.trim() || null;
    const cleanUserRetailerId = userRetailerId?.trim() || null;

    // Validate maxRetailers parameter
    const validMaxRetailers = Math.max(1, Math.min(maxRetailers, 100)); // Limit between 1-100

    logger.info("Regional market analysis by location request:", {
      city: cleanCity,
      state: cleanState,
      userRetailerId: cleanUserRetailerId || "none",
      maxRetailers: validMaxRetailers,
    });

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          logger.info(
            `Retrying regional analysis (attempt ${attempt}/${maxRetries}) after ${delay}ms delay`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        const startTime = Date.now();
        const { data, error } = await this.client.rpc(
          "get_regional_market_data_by_location",
          {
            target_city: cleanCity,
            target_state: cleanState,
            user_retailer_id: cleanUserRetailerId,
            max_retailers: validMaxRetailers,
          }
        );

        const duration = Date.now() - startTime;

        if (error) {
          throw new Error(`Supabase RPC error: ${error.message}`);
        }

        logger.info(
          "Regional market data by location retrieved successfully:",
          {
            duration: `${duration}ms`,
            recordCount: data?.length || 0,
            city: cleanCity,
            state: cleanState,
            attempt,
          }
        );

        return data || [];
      } catch (error: any) {
        lastError = error;
        logger.warn(
          `Regional analysis by location attempt ${attempt} failed:`,
          {
            error: error.message,
            attempt,
            city: cleanCity,
            state: cleanState,
          }
        );

        // Retry logic similar to market snapshot
        if (
          error.message?.includes("timeout") ||
          error.message?.includes("connection")
        ) {
          continue;
        } else if (
          error.message?.includes("function") &&
          error.message?.includes("does not exist")
        ) {
          break;
        }
      }
    }

    logger.error(
      "Regional market analysis by location failed after all retries:",
      {
        error: lastError?.message,
        city: cleanCity,
        state: cleanState,
        maxRetries,
      }
    );

    throw (
      lastError ||
      new Error(
        "Regional market analysis by location failed after multiple attempts"
      )
    );
  }

  /**
   * Get regional market data by radius from a central point
   */
  async getRegionalMarketDataByRadius(
    centerLatitude: number,
    centerLongitude: number,
    radiusMiles: number = 30,
    userRetailerId?: string,
    maxRetailers: number = 50
  ): Promise<any> {
    const maxRetries = 3;
    const baseDelay = 1000;

    // Input validation
    if (!centerLatitude || !centerLongitude) {
      throw new Error(
        "Both latitude and longitude must be provided for radius-based analysis"
      );
    }

    // Validate coordinates
    if (Math.abs(centerLatitude) > 90 || Math.abs(centerLongitude) > 180) {
      throw new Error("Invalid coordinates provided");
    }

    // Validate radius
    const validRadius = Math.max(1, Math.min(radiusMiles, 500)); // Limit between 1-500 miles
    const validMaxRetailers = Math.max(1, Math.min(maxRetailers, 100)); // Limit between 1-100
    const cleanUserRetailerId = userRetailerId?.trim() || null;

    logger.info("Regional market analysis by radius request:", {
      centerLatitude,
      centerLongitude,
      radiusMiles: validRadius,
      userRetailerId: cleanUserRetailerId || "none",
      maxRetailers: validMaxRetailers,
    });

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          logger.info(
            `Retrying radius analysis (attempt ${attempt}/${maxRetries}) after ${delay}ms delay`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        const startTime = Date.now();
        const { data, error } = await this.client.rpc(
          "get_regional_market_data_by_radius",
          {
            center_latitude: centerLatitude,
            center_longitude: centerLongitude,
            radius_miles: validRadius,
            user_retailer_id: cleanUserRetailerId,
            max_retailers: validMaxRetailers,
          }
        );

        const duration = Date.now() - startTime;

        if (error) {
          throw new Error(`Supabase RPC error: ${error.message}`);
        }

        logger.info("Regional market data by radius retrieved successfully:", {
          duration: `${duration}ms`,
          recordCount: data?.length || 0,
          centerLatitude,
          centerLongitude,
          radiusMiles: validRadius,
          attempt,
        });

        return data || [];
      } catch (error: any) {
        lastError = error;
        logger.warn(`Regional analysis by radius attempt ${attempt} failed:`, {
          error: error.message,
          attempt,
          centerLatitude,
          centerLongitude,
          radiusMiles: validRadius,
        });

        // Retry logic
        if (
          error.message?.includes("timeout") ||
          error.message?.includes("connection")
        ) {
          continue;
        } else if (
          error.message?.includes("function") &&
          error.message?.includes("does not exist")
        ) {
          break;
        }
      }
    }

    logger.error(
      "Regional market analysis by radius failed after all retries:",
      {
        error: lastError?.message,
        centerLatitude,
        centerLongitude,
        radiusMiles: validRadius,
        maxRetries,
      }
    );

    throw (
      lastError ||
      new Error(
        "Regional market analysis by radius failed after multiple attempts"
      )
    );
  }

  /**
   * Get data quality statistics for market analysis (optional helper)
   */
  async getMarketDataQualityStats(
    competitorPlaceIds: string[],
    userRetailerId?: string
  ): Promise<any> {
    try {
      const { data, error } = await this.client.rpc(
        "get_market_data_quality_stats",
        {
          competitor_place_ids: competitorPlaceIds,
          user_retailer_id: userRetailerId,
        }
      );

      if (error) {
        logger.error("Error fetching market data quality stats:", error);
        return null;
      }

      return data?.[0] || null;
    } catch (error) {
      logger.error("Error in getMarketDataQualityStats:", error);
      return null;
    }
  }

  /**
   * Search events by name, city, and/or state
   */
  async searchEvents(
    query?: string,
    city?: string,
    state?: string,
    limit: number = 20,
    page: number = 1
  ) {
    try {
      // Calculate offset for pagination
      const offset = (page - 1) * limit;

      // Build the query
      let queryBuilder = this.client
        .from("events")
        .select("*", { count: "exact" });

      // Only show future events - filter out events that have already started
      // Since start_time is stored as TEXT, we need to handle the comparison properly
      const now = new Date().toISOString();
      queryBuilder = queryBuilder.or(
        `start_time.gte.${now},start_time.is.null`
      );

      const queryTrimmed = query?.trim();
      const cityTrimmed = city?.trim();
      const stateTrimmed = state?.trim();

      // If we have a search query, search across event name, city, and state
      if (queryTrimmed) {
        queryBuilder = queryBuilder.or(
          `event_name.ilike.%${queryTrimmed}%,city.ilike.%${queryTrimmed}%,state.ilike.%${queryTrimmed}%`
        );
      }

      // If we have specific city and/or state filters (for location-based searches)
      if (cityTrimmed && stateTrimmed) {
        // Exact match for both city and state (case insensitive)
        queryBuilder = queryBuilder
          .ilike("city", cityTrimmed)
          .ilike("state", stateTrimmed);
      } else if (cityTrimmed) {
        // Exact match for city only
        queryBuilder = queryBuilder.ilike("city", cityTrimmed);
      } else if (stateTrimmed) {
        // Exact match for state only
        queryBuilder = queryBuilder.ilike("state", stateTrimmed);
      }

      // Apply pagination and ordering - order by date first, then by creation time
      queryBuilder = queryBuilder
        .order("start_time", { ascending: true, nullsFirst: false })
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: events, error, count } = await queryBuilder;

      if (error) {
        logger.error("Error searching events in Supabase:", error);
        throw error;
      }

      return {
        events: events || [],
        total_count: count || 0,
        page,
        limit,
        total_pages: Math.ceil((count || 0) / limit),
      };
    } catch (error) {
      logger.error("Error searching events:", error);
      throw error;
    }
  }

  /**
   * Get events for a specific location
   */
  async getLocationEvents(
    locationId: number,
    options: {
      search?: string;
      futureOnly?: boolean;
      page?: number;
      limit?: number;
    } = {}
  ) {
    try {
      const { search, futureOnly = true, page = 1, limit = 20 } = options;

      // Calculate offset for pagination
      const offset = (page - 1) * limit;

      // Build the query
      let queryBuilder = this.client
        .from("events")
        .select("*", { count: "exact" })
        .eq("location_id", locationId);

      // Only show future events if requested
      if (futureOnly) {
        const now = new Date().toISOString();
        queryBuilder = queryBuilder.or(
          `start_time.gte.${now},start_time.is.null`
        );
      }

      // Apply search filter
      if (search && search.trim()) {
        queryBuilder = queryBuilder.ilike("event_name", `%${search.trim()}%`);
      }

      // Apply pagination and ordering
      queryBuilder = queryBuilder
        .order("start_time", { ascending: true, nullsFirst: false })
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: events, error, count } = await queryBuilder;

      if (error) {
        logger.error("Error getting location events:", error);
        throw error;
      }

      return {
        events: events || [],
        total_count: count || 0,
        page,
        limit,
        total_pages: Math.ceil((count || 0) / limit),
      };
    } catch (error) {
      logger.error("Error getting location events:", error);
      throw error;
    }
  }

  /**
   * Create a new event
   */
  async createEvent(eventData: any) {
    try {
      const { data, error } = await this.client
        .from("events")
        .insert(eventData)
        .select()
        .single();

      if (error) {
        logger.error("Error creating event:", error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error("Error creating event:", error);
      throw error;
    }
  }

  /**
   * Get event by ID (with optional location verification)
   */
  async getEventById(eventId: string, locationId?: number) {
    try {
      let queryBuilder = this.client
        .from("events")
        .select("*")
        .eq("event_id", eventId);

      // If locationId is provided, verify the event belongs to that location
      if (locationId !== undefined) {
        queryBuilder = queryBuilder.eq("location_id", locationId);
      }

      const { data, error } = await queryBuilder.single();

      if (error) {
        if (error.code === "PGRST116") {
          // No rows returned
          return null;
        }
        logger.error("Error getting event by ID:", error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error("Error getting event by ID:", error);
      throw error;
    }
  }

  /**
   * Update an event
   */
  async updateEvent(eventId: string, locationId: number, updateData: any) {
    try {
      const { data, error } = await this.client
        .from("events")
        .update(updateData)
        .eq("event_id", eventId)
        .eq("location_id", locationId)
        .select()
        .single();

      if (error) {
        logger.error("Error updating event:", error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error("Error updating event:", error);
      throw error;
    }
  }

  /**
   * Delete an event
   */
  async deleteEvent(eventId: string, locationId: number) {
    try {
      const { error } = await this.client
        .from("events")
        .delete()
        .eq("event_id", eventId)
        .eq("location_id", locationId);

      if (error) {
        logger.error("Error deleting event:", error);
        throw error;
      }

      return true;
    } catch (error) {
      logger.error("Error deleting event:", error);
      throw error;
    }
  }
}
