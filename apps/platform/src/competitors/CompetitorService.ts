/* eslint-disable indent */
import { SupabaseService } from "../supabase/SupabaseService";
import { logger } from "../config/logger";
import App from "../app";

export interface CompetitorResult {
  place_id: string;
  name: string;
  address: string;
  location: {
    lat: number;
    lng: number;
  };
  distance: number;
  productCount?: number;
  isFromOurDatabase?: boolean;
}

export interface RetailerSearchResult {
  retailers: any[];
  total?: number;
  page?: number;
  pageSize?: number;
  totalPages?: number;
}

export class CompetitorService {
  private supabaseService: SupabaseService;

  constructor() {
    this.supabaseService = new SupabaseService({
      url: process.env.SUPABASE_URL || "",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
      timeout: process.env.SUPABASE_TIMEOUT
        ? parseInt(process.env.SUPABASE_TIMEOUT)
        : 30000, // 30 seconds default
    });
  }

  /**
   * Search for retailers by query - DATABASE ONLY (for competitor search)
   */
  async searchRetailers(query: string, limit: number = 10): Promise<any[]> {
    try {
      // Only search in Supabase database - no Google Places fallback for competitor search
      const supabaseResults = await this.supabaseService.searchRetailers(
        query,
        limit
      );

      logger.info(
        `Found ${supabaseResults.length} retailers in database for competitor search query: ${query}`
      );
      return supabaseResults;
    } catch (error) {
      logger.error("Error searching retailers for competitors:", error);
      throw error;
    }
  }

  /**
   * Search for business retailers with enhanced logic and Google Places fallback
   * Used specifically for business search in onboarding
   */
  async searchBusinessRetailers(query: string, limit: number = 10): Promise<any[]> {
    try {
      const words = query.trim().split(/\s+/);
      let supabaseResults: any[] = [];

      if (words.length < 3) {
        // If less than 3 words, search city or state from the database
        logger.info(
          `Business search: ${words.length} words detected, searching by city/state for query: ${query}`
        );
        supabaseResults = await this.supabaseService.searchRetailersByCityState(
          query,
          limit
        );
      } else {
        // If 3 or more words, search the name field of the retailer from the database
        logger.info(
          `Business search: ${words.length} words detected, searching by name for query: ${query}`
        );
        supabaseResults = await this.supabaseService.searchRetailersByNameOnly(
          query,
          limit
        );
      }

      // If we found results in Supabase, return them
      if (supabaseResults && supabaseResults.length > 0) {
        logger.info(
          `Found ${supabaseResults.length} business retailers in database for query: ${query}`
        );
        return supabaseResults;
      }

      // If no results from Supabase, fall back to Google Places API
      logger.info(
        `No results in database for business search query: ${query}, falling back to Google Places`
      );

      try {
        const googleResults = await this.searchCannabisBusinessInGooglePlaces(
          query,
          limit
        );
        logger.info(
          `Found ${googleResults.length} cannabis business retailers from Google Places for query: ${query}`
        );
        return googleResults;
      } catch (googleError) {
        logger.error("Google Places fallback failed for cannabis business search:", googleError);
        // Return empty array if both Supabase and Google fail
        return [];
      }
    } catch (error) {
      logger.error("Error searching business retailers:", error);
      throw error;
    }
  }

  /**
   * Search cannabis business retailers using Google Places API with CBR context
   */
  private async searchCannabisBusinessInGooglePlaces(
    query: string,
    limit: number = 10
  ): Promise<any[]> {
    const axios = (await import("axios")).default;

    try {
      // Enhanced query with cannabis business context
      const cannabisQueries = this.buildCannabisSearchQueries(query);

      logger.info(`Searching Google Places for cannabis businesses with queries: ${cannabisQueries.join(', ')}`);

      const allResults: any[] = [];

      // Try multiple cannabis-specific search queries
      for (const cannabisQuery of cannabisQueries) {
        try {
          const response = await axios.post(
            "https://places.googleapis.com/v1/places:autocomplete",
            {
              input: cannabisQuery.trim(),
              includedPrimaryTypes: ["store", "establishment"], // Focus on business establishments
              locationBias: {
                // Bias towards US locations where cannabis is more likely to be legal
                rectangle: {
                  low: { latitude: 25.0, longitude: -125.0 },
                  high: { latitude: 49.0, longitude: -66.0 }
                }
              }
            },
            {
              headers: {
                "X-Goog-Api-Key": process.env.GOOGLE_MAPS_API_KEY,
              },
            }
          );

          if (response.data?.suggestions && response.data.suggestions.length > 0) {
            // Filter results to prioritize cannabis-related businesses
            const filteredSuggestions = this.filterCannabisRelatedResults(
              response.data.suggestions,
              query
            );

            if (filteredSuggestions.length > 0) {
              allResults.push(...filteredSuggestions);
              break; // Stop searching if we found relevant results
            }
          }
        } catch (queryError) {
          logger.warn(`Failed to search with query "${cannabisQuery}":`, queryError);
          continue; // Try next query
        }
      }

      if (allResults.length === 0) {
        logger.info(`No cannabis business results found for query: ${query}`);
        return [];
      }

      // Transform Google Places results to our retailer format
      const googleRetailers = await Promise.all(
        allResults
          .slice(0, limit)
          .map(async (suggestion: any) => {
            try {
              const placeDetails = suggestion.placePrediction;

              // Get place details including coordinates
              let coordinates = { lat: 0, lng: 0 };
              let formattedAddress = placeDetails.text?.text || "";

              try {
                const geocodeResponse = await axios.post(
                  `https://maps.googleapis.com/maps/api/geocode/json?place_id=${placeDetails.placeId.trim()}&key=${
                    process.env.GOOGLE_MAPS_API_KEY
                  }`
                );

                if (geocodeResponse.data?.results?.[0]) {
                  const result = geocodeResponse.data.results[0];
                  if (result.geometry?.location) {
                    coordinates = {
                      lat: result.geometry.location.lat || 0,
                      lng: result.geometry.location.lng || 0,
                    };
                  }
                  formattedAddress =
                    result.formatted_address || formattedAddress;
                }
              } catch (geocodeError) {
                logger.warn(
                  `Failed to geocode place ${placeDetails.placeId}:`,
                  geocodeError
                );
              }

              // Parse address components
              const addressParts = formattedAddress.split(", ");
              const zipMatch = formattedAddress.match(/\b\d{5}(-\d{4})?\b/);
              const stateMatch = formattedAddress.match(/\b[A-Z]{2}\b/);

              return {
                retailer_id: placeDetails.placeId,
                id: placeDetails.placeId,
                name:
                  placeDetails.structuredFormat?.mainText?.text ||
                  placeDetails.text?.text ||
                  "Unknown Business",
                dispensary_name:
                  placeDetails.structuredFormat?.mainText?.text ||
                  placeDetails.text?.text ||
                  "Unknown Business",
                address: addressParts[0] || "",
                physical_address: addressParts[0] || "",
                city: addressParts[1] || "",
                state: stateMatch?.[0] || "",
                zip_code: zipMatch?.[0] || "",
                country: "US",
                latitude: coordinates.lat,
                longitude: coordinates.lng,
                contact_phone: "",
                contact_email: "",
                website_url: "",
                serves_medical_users: false,
                serves_recreational_users: false,
                isFromOurDatabase: false,
                product_count: 0,
                productCount: 0,
              };
            } catch (error) {
              logger.error(`Error processing Google Places result:`, error);
              return null;
            }
          })
      );

      // Filter out null results
      const validRetailers = googleRetailers.filter((retailer) => retailer !== null);

      logger.info(`Successfully processed ${validRetailers.length} cannabis business results from Google Places`);
      return validRetailers;
    } catch (error) {
      logger.error("Error searching cannabis businesses in Google Places:", error);
      throw error;
    }
  }

  /**
   * Build cannabis-specific search queries
   */
  private buildCannabisSearchQueries(originalQuery: string): string[] {
    const cannabisTerms = [
      "dispensary",
      "cannabis",
      "marijuana",
      "weed",
      "cbd",
      "hemp",
      "medical marijuana",
      "recreational cannabis"
    ];

    const queries: string[] = [];

    // If the query already contains cannabis terms, use it as-is first
    const lowerQuery = originalQuery.toLowerCase();
    const hasCannabisTerm = cannabisTerms.some(term => lowerQuery.includes(term));

    if (hasCannabisTerm) {
      queries.push(originalQuery);
    }

    // Add cannabis context to the original query
    queries.push(`${originalQuery} dispensary`);
    queries.push(`${originalQuery} cannabis`);
    queries.push(`cannabis dispensary ${originalQuery}`);

    // If it's a location-based query (< 3 words), add more specific searches
    const words = originalQuery.trim().split(/\s+/);
    if (words.length < 3) {
      queries.push(`dispensary in ${originalQuery}`);
      queries.push(`cannabis store ${originalQuery}`);
      queries.push(`marijuana dispensary ${originalQuery}`);
    }

    return queries;
  }

  /**
   * Filter Google Places results to prioritize cannabis-related businesses
   */
  private filterCannabisRelatedResults(suggestions: any[], originalQuery: string): any[] {
    const cannabisKeywords = [
      'dispensary', 'cannabis', 'marijuana', 'weed', 'cbd', 'hemp',
      'medical marijuana', 'recreational', 'thc', 'ganja', 'pot',
      'green', 'leaf', 'herb', 'bud', 'flower', 'concentrate'
    ];

    const locationKeywords = ['store', 'shop', 'clinic', 'pharmacy', 'collective', 'co-op'];

    return suggestions.filter((suggestion: any) => {
      const text = (suggestion.placePrediction?.text?.text || '').toLowerCase();
      const mainText = (suggestion.placePrediction?.structuredFormat?.mainText?.text || '').toLowerCase();
      const secondaryText = (suggestion.placePrediction?.structuredFormat?.secondaryText?.text || '').toLowerCase();

      const fullText = `${text} ${mainText} ${secondaryText}`;

      // Prioritize results that contain cannabis-related keywords
      const hasCannabisKeyword = cannabisKeywords.some(keyword => fullText.includes(keyword));
      const hasLocationKeyword = locationKeywords.some(keyword => fullText.includes(keyword));

      // Include if it has cannabis keywords, or if it's a store/shop and matches the original query
      return hasCannabisKeyword || (hasLocationKeyword && fullText.includes(originalQuery.toLowerCase()));
    });
  }

  /**
   * Search retailers using Google Places API (legacy method for competitor search)
   */
  private async searchRetailersInGooglePlaces(
    query: string,
    limit: number = 10
  ): Promise<any[]> {
    const axios = (await import("axios")).default;

    try {
      // Use Google Places autocomplete to find businesses
      const response = await axios.post(
        "https://places.googleapis.com/v1/places:autocomplete",
        {
          input: query.trim(),
        },
        {
          headers: {
            "X-Goog-Api-Key": process.env.GOOGLE_MAPS_API_KEY,
          },
        }
      );

      if (
        !response.data?.suggestions ||
        response.data.suggestions.length === 0
      ) {
        return [];
      }

      // Transform Google Places results to our retailer format
      const googleRetailers = await Promise.all(
        response.data.suggestions
          .slice(0, limit)
          .map(async (suggestion: any) => {
            try {
              const placeDetails = suggestion.placePrediction;

              // Get place details including coordinates
              let coordinates = { lat: 0, lng: 0 };
              let formattedAddress = placeDetails.text?.text || "";

              try {
                const geocodeResponse = await axios.post(
                  `https://maps.googleapis.com/maps/api/geocode/json?place_id=${placeDetails.placeId.trim()}&key=${
                    process.env.GOOGLE_MAPS_API_KEY
                  }`
                );

                if (geocodeResponse.data?.results?.[0]) {
                  const result = geocodeResponse.data.results[0];
                  if (result.geometry?.location) {
                    coordinates = {
                      lat: result.geometry.location.lat || 0,
                      lng: result.geometry.location.lng || 0,
                    };
                  }
                  formattedAddress =
                    result.formatted_address || formattedAddress;
                }
              } catch (geocodeError) {
                logger.warn(
                  `Failed to geocode place ${placeDetails.placeId}:`,
                  geocodeError
                );
              }

              // Parse address components
              const addressParts = formattedAddress.split(", ");
              const zipMatch = formattedAddress.match(/\b\d{5}(-\d{4})?\b/);
              const stateMatch = formattedAddress.match(/\b[A-Z]{2}\b/);

              return {
                retailer_id: placeDetails.placeId,
                id: placeDetails.placeId,
                name:
                  placeDetails.structuredFormat?.mainText?.text ||
                  placeDetails.text?.text ||
                  "Unknown Business",
                dispensary_name:
                  placeDetails.structuredFormat?.mainText?.text ||
                  placeDetails.text?.text ||
                  "Unknown Business",
                address: addressParts[0] || "",
                physical_address: addressParts[0] || "",
                city: addressParts[1] || "",
                state: stateMatch?.[0] || "",
                zip_code: zipMatch?.[0] || "",
                country: "US",
                latitude: coordinates.lat,
                longitude: coordinates.lng,
                contact_phone: "",
                contact_email: "",
                website_url: "",
                serves_medical_users: false,
                serves_recreational_users: false,
                isFromOurDatabase: false,
                product_count: 0,
                productCount: 0,
              };
            } catch (error) {
              logger.error(`Error processing Google Places result:`, error);
              return null;
            }
          })
      );

      // Filter out null results
      return googleRetailers.filter((retailer) => retailer !== null);
    } catch (error) {
      logger.error("Error searching Google Places:", error);
      throw error;
    }
  }

  /**
   * Search for nearby retailers
   */
  async searchNearbyRetailers(
    latitude: number,
    longitude: number,
    radiusMiles: number = 30,
    limit: number = 8,
    page: number = 1,
    pageSize: number = 4,
    preferredState?: string
  ): Promise<RetailerSearchResult> {
    try {
      const retailers = await this.supabaseService.searchNearbyRetailers(
        latitude,
        longitude,
        radiusMiles,
        limit * 2,
        preferredState
      );

      // Handle pagination
      const startIndex = (page - 1) * pageSize;
      const paginatedRetailers = retailers.slice(
        startIndex,
        startIndex + pageSize
      );

      return {
        retailers: paginatedRetailers,
        total: retailers.length,
        page,
        pageSize,
        totalPages: Math.ceil(retailers.length / pageSize),
      };
    } catch (error) {
      logger.error("Error searching nearby retailers:", error);
      throw error;
    }
  }

  /**
   * Search for retailers by location (city, state, zip)
   */
  async searchRetailersByLocation(
    city?: string,
    state?: string,
    zip?: string,
    radiusMiles: number = 30,
    page: number = 1,
    pageSize: number = 10
  ): Promise<RetailerSearchResult> {
    try {
      console.log(
        `🏙️ CompetitorService: Searching retailers by location: city="${city}", state="${state}", zip="${zip}"`
      );

      const retailers = await this.supabaseService.searchRetailersByLocation(
        city,
        state,
        zip,
        radiusMiles,
        pageSize * 2 // Get more results to ensure we have enough after filtering
      );

      console.log(
        `📍 CompetitorService: Found ${retailers.length} retailers by location`
      );

      // Handle pagination
      const startIndex = (page - 1) * pageSize;
      const paginatedRetailers = retailers.slice(
        startIndex,
        startIndex + pageSize
      );

      return {
        retailers: paginatedRetailers,
        total: retailers.length,
        page,
        pageSize,
        totalPages: Math.ceil(retailers.length / pageSize),
      };
    } catch (error) {
      logger.error("Error searching retailers by location:", error);
      throw error;
    }
  }

  /**
   * Get retailer by ID
   */
  async getRetailerById(retailerId: string): Promise<any> {
    try {
      return await this.supabaseService.getRetailerById(retailerId);
    } catch (error) {
      logger.error(`Error getting retailer ${retailerId}:`, error);
      throw error;
    }
  }

  /**
   * Get retailer products
   */
  async getRetailerProducts(
    placeId: string,
    limit: number = 100
  ): Promise<{ products: any[]; total_count: number }> {
    try {
      logger.info({
        message: "Starting getRetailerProducts",
        placeId,
        limit,
      });

      // Get products from Supabase
      const response = await this.supabaseService.getRetailerProducts(
        placeId,
        limit
      );

      logger.info({
        message: "Received response from Supabase",
        placeId,
        hasProducts: Boolean(response.products),
        productCount: response.products?.length || 0,
        totalCount: response.total_count,
        sampleProducts: response.products?.slice(0, 3).map((p) => ({
          id: p.id,
          name: p.product_name,
          price: p.latest_price,
        })),
      });

      if (!response.products || response.products.length === 0) {
        logger.warn({
          message: "No products found in Supabase response",
          placeId,
          response,
        });
      }

      return {
        products: response.products || [],
        total_count: response.total_count || 0,
      };
    } catch (error) {
      logger.error({
        message: "Error in getRetailerProducts",
        placeId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * Get only the count of products for a retailer
   */
  async getRetailerProductCount(retailerId: string): Promise<number> {
    try {
      const result = await this.supabaseService.getRetailerProductCount(
        retailerId
      );
      return result?.count || 0;
    } catch (error) {
      logger.error(
        `Error getting product count for retailer ${retailerId}:`,
        error
      );
      return 0; // Return 0 on error rather than throwing
    }
  }

  /**
   * Perform comprehensive market analysis across competitors with weight-aware pricing,
   * product counts, competitor breakdowns, and detailed insights.
   */
  async performMarketAnalysis(
    competitors: CompetitorResult[],
    userRetailerId?: string
  ): Promise<any> {
    try {
      const competitorPlaceIds = competitors
        .map((c) => c.place_id)
        .filter(Boolean);

      if (competitorPlaceIds.length === 0 && !userRetailerId) {
        return {
          // Return a default empty state
          competitorCount: 0,
          hotCategories: [],
          gaps: [],
          insights: ["Add competitors to see market insights."],
          recommendations: [],
        };
      }

      const snapshotData = await this.supabaseService.getMarketSnapshotData(
        competitorPlaceIds,
        userRetailerId
      );

      if (!snapshotData || snapshotData.length === 0) {
        return {
          competitorCount: competitors.length,
          hotCategories: [],
          gaps: ["No product data found for the selected competitors."],
          insights: [],
          recommendations: [],
        };
      }

      // Process snapshot data
      const userProducts = snapshotData.filter((p: any) => p.is_user_product);
      const competitorProducts = snapshotData.filter(
        (p: any) => !p.is_user_product
      );

      // Get all available categories from competitor data
      const availableCategories = [
        ...new Set(competitorProducts.map((p: any) => p.category)),
      ].filter(Boolean) as string[];

      const hotCategories = availableCategories
        .map((category: string) => {
          const categoryProducts = competitorProducts.filter(
            (p: any) => p.category.toLowerCase() === category.toLowerCase()
          );
          if (categoryProducts.length === 0) return null;

          // For Flower, show multiple weights; for others, show top 2 weights
          const isFlowerCategory = category.toLowerCase() === "flower";
          const maxWeightsToShow = isFlowerCategory ? 5 : 2;

          // Find the most common weights for this category
          const weightCounts = categoryProducts.reduce(
            (acc: Record<string, number>, p: any) => {
              if (p.display_weight) {
                acc[p.display_weight] = (acc[p.display_weight] || 0) + 1;
              }
              return acc;
            },
            {} as Record<string, number>
          );

          // Get top weights sorted by popularity
          const topWeights = Object.keys(weightCounts)
            .sort((a, b) => weightCounts[b] - weightCounts[a])
            .slice(0, maxWeightsToShow);

          // Create analysis for each weight
          const weightAnalysis = topWeights
            .map((weight) => {
              const weightProducts = categoryProducts.filter(
                (p: any) => p.display_weight === weight
              );

              if (weightProducts.length === 0) return null;

              const marketAvg =
                weightProducts.reduce(
                  (sum: number, p: any) => sum + Number(p.latest_price),
                  0
                ) / weightProducts.length;

              // Find user's average price for this specific weight
              const userWeightProducts = userProducts.filter(
                (p: any) =>
                  p.category.toLowerCase() === category.toLowerCase() &&
                  p.display_weight === weight
              );
              const yourAvg =
                userWeightProducts.length > 0
                  ? userWeightProducts.reduce(
                      (sum: number, p: any) => sum + Number(p.latest_price),
                      0
                    ) / userWeightProducts.length
                  : 0;

              const youVsMarketPct =
                yourAvg > 0 ? ((yourAvg - marketAvg) / marketAvg) * 100 : 0;

              const competitorCount = new Set(
                weightProducts.map((p: any) => p.retailer_id)
              ).size;

              const totalCompetitorProductCount = weightProducts.length;
              const yourProductCount = userWeightProducts.length;
              const averageCompetitorProductCount =
                competitorCount > 0
                  ? Math.round(totalCompetitorProductCount / competitorCount)
                  : 0;

              // Create breakdown by retailer_id
              const competitorBreakdown = competitors
                .map((comp) => {
                  const retailerProducts = weightProducts.filter(
                    (p: any) => p.retailer_id === comp.place_id
                  );
                  return {
                    retailerId: comp.place_id,
                    retailerName: comp.name,
                    productCount: retailerProducts.length,
                  };
                })
                .filter((comp) => comp.productCount > 0);

              return {
                category,
                weight,
                marketAvg: parseFloat(marketAvg.toFixed(2)),
                yourAvg: parseFloat(yourAvg.toFixed(2)),
                youVsMarketPct: parseFloat(youVsMarketPct.toFixed(2)),
                competitorCount,
                totalCompetitorProductCount,
                yourProductCount,
                averageCompetitorProductCount,
                competitorBreakdown,
              };
            })
            .filter(Boolean);

          return weightAnalysis;
        })
        .filter(Boolean)
        .flat() // Flatten the array since we now return arrays of weights
        .sort(
          (a, b) =>
            (b?.totalCompetitorProductCount || 0) -
            (a?.totalCompetitorProductCount || 0)
        ); // Sort by product count

      // Enhanced Gap Analysis
      const gaps = [];
      const competitorCategories = new Set(
        competitorProducts.map((p: any) => p.category.toLowerCase())
      );
      const userCategories = new Set(
        userProducts.map((p: any) => p.category.toLowerCase())
      );

      // Category gaps
      for (const cat of competitorCategories) {
        if (!userCategories.has(cat)) {
          const categoryName =
            (cat as string).charAt(0).toUpperCase() + (cat as string).slice(1);
          const competitorCount = new Set(
            competitorProducts
              .filter((p: any) => p.category.toLowerCase() === cat)
              .map((p: any) => p.retailer_id)
          ).size;
          gaps.push(
            `${categoryName} products are sold by ${competitorCount} competitors, but you don't carry them.`
          );
        }
      }

      // Price gaps - identify where user is significantly over/under market
      const priceGaps = hotCategories
        .filter((cat) => cat && cat.yourAvg > 0)
        .map((cat) => {
          if (!cat) return null;
          if (cat.youVsMarketPct > 25) {
            return `Your ${cat.category} (${
              cat.weight
            }) is ${cat.youVsMarketPct.toFixed(0)}% above market average ($${
              cat.marketAvg
            }).`;
          } else if (cat.youVsMarketPct < -25) {
            return `Your ${cat.category} (${cat.weight}) is ${Math.abs(
              cat.youVsMarketPct
            ).toFixed(0)}% below market average ($${cat.marketAvg}).`;
          }
          return null;
        })
        .filter(Boolean);

      gaps.push(...priceGaps);

      // Enhanced Insights
      const insights = [
        `We analyzed ${competitorProducts.length} products from ${competitors.length} competitors across ${availableCategories.length} categories.`,
      ];

      if (hotCategories.length > 0) {
        const topCategory = hotCategories[0];
        if (topCategory) {
          insights.push(
            `${topCategory.category} is the most popular category with ${topCategory.totalCompetitorProductCount} products from ${topCategory.competitorCount} competitors.`
          );
        }

        // Best deals
        const bestDeals = hotCategories
          .filter((c) => c && c.youVsMarketPct < -5 && c.yourAvg > 0)
          .sort((a, b) => (a?.youVsMarketPct || 0) - (b?.youVsMarketPct || 0))
          .slice(0, 2);

        bestDeals.forEach((deal) => {
          if (deal) {
            insights.push(
              `Your ${deal.category} (${
                deal.weight
              }) offers excellent value at ${Math.abs(
                deal.youVsMarketPct
              ).toFixed(0)}% below market average.`
            );
          }
        });

        // Overpriced items
        const overpriced = hotCategories
          .filter((c) => c && c.youVsMarketPct > 15 && c.yourAvg > 0)
          .sort((a, b) => (b?.youVsMarketPct || 0) - (a?.youVsMarketPct || 0))
          .slice(0, 1);

        overpriced.forEach((item) => {
          if (item) {
            insights.push(
              `Consider reviewing ${item.category} (${
                item.weight
              }) pricing - currently ${item.youVsMarketPct.toFixed(
                0
              )}% above market average.`
            );
          }
        });
      }

      // Category-level inventory comparison
      const categoryComparison = availableCategories
        .map((category: string) => {
          // Get all competitor retailers for this category
          const competitorRetailers = competitors
            .map((comp) => {
              const retailerProducts = competitorProducts.filter(
                (p: any) =>
                  p.category.toLowerCase() === category.toLowerCase() &&
                  p.retailer_id === comp.place_id
              );
              return {
                retailerId: comp.place_id,
                retailerName: comp.name,
                productCount: retailerProducts.length,
              };
            })
            .filter((comp) => comp.productCount > 0);

          // Get user's product count for this category
          const userProductCount = userProducts.filter(
            (p: any) => p.category.toLowerCase() === category.toLowerCase()
          ).length;

          const totalCompetitorProducts = competitorProducts.filter(
            (p: any) => p.category.toLowerCase() === category.toLowerCase()
          ).length;

          return {
            category,
            yourProductCount: userProductCount,
            competitorBreakdown: competitorRetailers,
            totalCompetitorProducts,
            averageCompetitorProducts:
              competitorRetailers.length > 0
                ? Math.round(
                    totalCompetitorProducts / competitorRetailers.length
                  )
                : 0,
          };
        })
        .filter((cat) => cat.totalCompetitorProducts > 0)
        .sort((a, b) => b.totalCompetitorProducts - a.totalCompetitorProducts);

      // Enhanced Recommendations
      const recommendations = [];

      if (gaps.length > 0) {
        recommendations.push(
          "Consider expanding your product assortment to match competitor offerings."
        );
      }

      if (hotCategories.some((c) => c && c.yourAvg === 0)) {
        recommendations.push(
          "Add pricing for categories where competitors are active to capture market share."
        );
      }

      const highMarginOpportunities = hotCategories.filter(
        (c) => c && c.yourAvg === 0 && c.marketAvg > 50
      );
      if (highMarginOpportunities.length > 0) {
        recommendations.push(
          "Focus on high-value categories where competitors are seeing strong pricing."
        );
      }

      // Add inventory-based recommendations
      const underStockedCategories = categoryComparison.filter(
        (cat) => cat.yourProductCount < cat.averageCompetitorProducts * 0.5
      );
      if (underStockedCategories.length > 0) {
        recommendations.push(
          `Consider expanding inventory in ${underStockedCategories
            .slice(0, 2)
            .map((c) => c.category)
            .join(" and ")} - you have fewer products than competitors.`
        );
      }

      const overStockedCategories = categoryComparison.filter(
        (cat) =>
          cat.yourProductCount > cat.averageCompetitorProducts * 2 &&
          cat.yourProductCount > 20
      );
      if (overStockedCategories.length > 0) {
        recommendations.push(
          `You have strong inventory depth in ${overStockedCategories
            .slice(0, 2)
            .map((c) => c.category)
            .join(" and ")} - leverage this competitive advantage.`
        );
      }

      recommendations.push(
        "Monitor competitor pricing regularly for market positioning."
      );

      return {
        competitorCount: competitors.length,
        hotCategories: hotCategories.slice(0, 15), // Increased to show more weights
        gaps: gaps.slice(0, 8), // Limit to most important gaps
        insights,
        recommendations,
        categoryComparison, // New field with inventory comparisons
        generatedAt: new Date().toISOString(),
        totalProductsAnalyzed: competitorProducts.length,
        categoriesFound: availableCategories.length,
      };
    } catch (error) {
      logger.error("Error in market analysis:", error);
      throw error;
    }
  }

  /**
   * Save competitor to database for a location
   */
  async saveCompetitor(locationId: number, competitor: any): Promise<any> {
    logger.info("Saving competitor:", { locationId, competitor });

    try {
      const competitorData = {
        location_id: locationId,
        competitor_place_id: competitor.place_id || `custom-${Date.now()}`,
        name: competitor.name,
        address: competitor.address,
        latitude: competitor.location?.lat || 0,
        longitude: competitor.location?.lng || 0,
        distance_km: competitor.distance || 0,
        created_at: new Date(),
        updated_at: new Date(),
      };

      logger.info("Inserting competitor data:", competitorData);

      // Insert the competitor
      const [id] = await App.main.db("location_competitors").insert(competitorData);

      logger.info("Competitor inserted with ID:", id);

      return { id, ...competitor, location_id: locationId };
    } catch (error) {
      logger.error(
        `Error saving competitor for location ${locationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get competitors for a location
   */
  async getCompetitors(locationId: number): Promise<CompetitorResult[]> {
    try {
      const competitors = await App.main
        .db("location_competitors")
        .where("location_id", locationId)
        .select("*");

      return competitors.map((competitor) => ({
        place_id: competitor.competitor_place_id,
        retailer_id: competitor.competitor_place_id,
        name: competitor.name,
        address: competitor.address,
        location: {
          lat: competitor.latitude,
          lng: competitor.longitude,
        },
        distance: competitor.distance_km,
      }));
    } catch (error) {
      logger.error(
        `Error getting competitors for location ${locationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Remove competitor from a location
   */
  async removeCompetitor(
    locationId: number,
    placeId: string
  ): Promise<boolean> {
    try {
      await App.main
        .db("location_competitors")
        .where({
          location_id: locationId,
          competitor_place_id: placeId,
        })
        .delete();

      return true;
    } catch (error) {
      logger.error(
        `Error removing competitor ${placeId} from location ${locationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get raw competitors data for a location
   * This includes all database fields with no transformation
   */
  async getRawCompetitors(locationId: number): Promise<any[]> {
    try {
      const competitors = await App.main
        .db("location_competitors")
        .where("location_id", locationId)
        .select("*");

      logger.info({
        message: "Retrieved raw competitor data",
        locationId,
        count: competitors.length,
        fields: competitors.length > 0 ? Object.keys(competitors[0]) : [],
      });

      return competitors;
    } catch (error) {
      logger.error(
        `Error getting raw competitors for location ${locationId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get regional market analysis by city and/or state
   * Analyzes market data across a broader geographic region
   */
  async getRegionalMarketAnalysisByLocation(
    city?: string,
    state?: string,
    userRetailerId?: string,
    maxRetailers: number = 50
  ): Promise<any> {
    try {
      logger.info("Starting regional market analysis by location:", {
        city: city || "ANY",
        state: state || "ANY",
        userRetailerId: userRetailerId || "none",
        maxRetailers,
      });

      const regionalData =
        await this.supabaseService.getRegionalMarketDataByLocation(
          city,
          state,
          userRetailerId,
          maxRetailers
        );

      if (!regionalData || regionalData.length === 0) {
        return {
          analysisType: "regional-location",
          location: { city, state },
          competitorCount: 0,
          hotCategories: [],
          gaps: ["No regional market data found for the specified location."],
          insights: ["Try expanding the search to include neighboring areas."],
          recommendations: [],
          regionStats: {
            totalRetailers: 0,
            totalProducts: 0,
            citiesAnalyzed: [],
            topRetailers: [],
          },
        };
      }

      // Process regional data
      const userProducts = regionalData.filter((p: any) => p.is_user_product);
      const competitorProducts = regionalData.filter(
        (p: any) => !p.is_user_product
      );

      // Get unique retailers and cities in the region
      const uniqueRetailers = [
        ...new Set(competitorProducts.map((p: any) => p.retailer_id)),
      ];
      const uniqueCities = [
        ...new Set(
          competitorProducts.map((p: any) => p.retailer_city).filter(Boolean)
        ),
      ];

      // Get top retailers by product count
      const retailerProductCounts = competitorProducts.reduce(
        (
          acc: Record<string, { name: string; city: string; count: number }>,
          p: any
        ) => {
          if (!acc[p.retailer_id]) {
            acc[p.retailer_id] = {
              name: p.retailer_name,
              city: p.retailer_city,
              count: 0,
            };
          }
          acc[p.retailer_id].count++;
          return acc;
        },
        {}
      );

      const topRetailers = Object.entries(retailerProductCounts)
        .map(([id, data]) => ({
          retailerId: id,
          name: (data as any).name,
          city: (data as any).city,
          productCount: (data as any).count,
        }))
        .sort((a, b) => b.productCount - a.productCount)
        .slice(0, 10);

      // Category analysis similar to standard market analysis but with regional context
      const availableCategories = [
        ...new Set(competitorProducts.map((p: any) => p.category)),
      ].filter(Boolean) as string[];

      const hotCategories = availableCategories
        .map((category: string) => {
          const categoryProducts = competitorProducts.filter(
            (p: any) => p.category.toLowerCase() === category.toLowerCase()
          );
          if (categoryProducts.length === 0) return null;

          // For regional analysis, focus on top 3 weights per category
          const weightCounts = categoryProducts.reduce(
            (acc: Record<string, number>, p: any) => {
              if (p.display_weight) {
                acc[p.display_weight] = (acc[p.display_weight] || 0) + 1;
              }
              return acc;
            },
            {} as Record<string, number>
          );

          const topWeights = Object.keys(weightCounts)
            .sort((a, b) => weightCounts[b] - weightCounts[a])
            .slice(0, 3); // Top 3 weights for regional view

          const weightAnalysis = topWeights
            .map((weight) => {
              const weightProducts = categoryProducts.filter(
                (p: any) => p.display_weight === weight
              );

              if (weightProducts.length === 0) return null;

              const prices = weightProducts.map((p: any) =>
                Number(p.latest_price)
              );
              const marketAvg =
                prices.reduce((sum: number, price: number) => sum + price, 0) /
                prices.length;
              const marketMin = Math.min(...prices);
              const marketMax = Math.max(...prices);

              // User comparison
              const userWeightProducts = userProducts.filter(
                (p: any) =>
                  p.category.toLowerCase() === category.toLowerCase() &&
                  p.display_weight === weight
              );
              const yourAvg =
                userWeightProducts.length > 0
                  ? userWeightProducts.reduce(
                      (sum: number, p: any) => sum + Number(p.latest_price),
                      0
                    ) / userWeightProducts.length
                  : 0;

              const youVsMarketPct =
                yourAvg > 0 ? ((yourAvg - marketAvg) / marketAvg) * 100 : 0;

              const retailerCount = new Set(
                weightProducts.map((p: any) => p.retailer_id)
              ).size;
              const cityCount = new Set(
                weightProducts.map((p: any) => p.retailer_city).filter(Boolean)
              ).size;

              return {
                category,
                weight,
                marketAvg: parseFloat(marketAvg.toFixed(2)),
                marketMin: parseFloat(marketMin.toFixed(2)),
                marketMax: parseFloat(marketMax.toFixed(2)),
                yourAvg: parseFloat(yourAvg.toFixed(2)),
                youVsMarketPct: parseFloat(youVsMarketPct.toFixed(2)),
                retailerCount,
                productCount: weightProducts.length,
                cityCount,
                priceSpread: parseFloat((marketMax - marketMin).toFixed(2)),
              };
            })
            .filter(Boolean);

          return weightAnalysis;
        })
        .filter(Boolean)
        .flat()
        .sort((a, b) => (b?.productCount || 0) - (a?.productCount || 0));

      // Regional insights
      const insights = [
        `Analyzed ${competitorProducts.length} products from ${uniqueRetailers.length} retailers across ${uniqueCities.length} cities in the region.`,
      ];

      if (state && !city) {
        insights.push(
          `State-wide analysis for ${state} shows ${availableCategories.length} product categories.`
        );
      } else if (city && state) {
        insights.push(
          `${city}, ${state} market analysis across ${availableCategories.length} categories.`
        );
      }

      if (topRetailers.length > 0) {
        const topRetailer = topRetailers[0];
        insights.push(
          `${topRetailer.name} leads the region with ${topRetailer.productCount} products.`
        );
      }

      // Price variation insights
      const highVariationCategories = hotCategories
        .filter((c) => c && c.priceSpread > c.marketAvg * 0.5) // High variation if spread > 50% of average
        .slice(0, 3);

      if (highVariationCategories.length > 0) {
        insights.push(
          `High price variation detected in ${highVariationCategories
            .map((c) => c?.category)
            .join(", ")} - significant opportunity for competitive positioning.`
        );
      }

      // Regional recommendations
      const recommendations = [
        "Regional analysis provides broader market context for pricing decisions.",
        "Consider the price ranges across different cities for competitive positioning.",
      ];

      if (uniqueCities.length > 3) {
        recommendations.push(
          `With ${uniqueCities.length} cities analyzed, consider location-specific pricing strategies.`
        );
      }

      if (userRetailerId && userProducts.length === 0) {
        recommendations.push(
          "Your products were not found in this regional analysis - consider expanding distribution."
        );
      }

      return {
        analysisType: "regional-location",
        location: { city, state },
        competitorCount: uniqueRetailers.length,
        hotCategories: hotCategories.slice(0, 20),
        gaps: [], // Regional analysis focuses more on market overview than gaps
        insights,
        recommendations,
        regionStats: {
          totalRetailers: uniqueRetailers.length,
          totalProducts: competitorProducts.length,
          citiesAnalyzed: uniqueCities,
          topRetailers: topRetailers.slice(0, 5),
        },
        generatedAt: new Date().toISOString(),
        totalProductsAnalyzed: competitorProducts.length,
        categoriesFound: availableCategories.length,
      };
    } catch (error) {
      logger.error("Error in regional market analysis by location:", error);
      throw error;
    }
  }

  /**
   * Get regional market analysis by radius from a central point
   * Analyzes market data within a specified distance from coordinates
   */
  async getRegionalMarketAnalysisByRadius(
    centerLatitude: number,
    centerLongitude: number,
    radiusMiles: number = 30,
    userRetailerId?: string,
    maxRetailers: number = 50
  ): Promise<any> {
    try {
      logger.info("Starting regional market analysis by radius:", {
        centerLatitude,
        centerLongitude,
        radiusMiles,
        userRetailerId: userRetailerId || "none",
        maxRetailers,
      });

      const regionalData =
        await this.supabaseService.getRegionalMarketDataByRadius(
          centerLatitude,
          centerLongitude,
          radiusMiles,
          userRetailerId,
          maxRetailers
        );

      if (!regionalData || regionalData.length === 0) {
        return {
          analysisType: "regional-radius",
          location: {
            latitude: centerLatitude,
            longitude: centerLongitude,
            radiusMiles,
          },
          competitorCount: 0,
          hotCategories: [],
          gaps: [
            `No regional market data found within ${radiusMiles} miles of the specified location.`,
          ],
          insights: [
            "Try increasing the radius or checking if there are retailers in the area.",
          ],
          recommendations: [],
          regionStats: {
            totalRetailers: 0,
            totalProducts: 0,
            averageDistance: 0,
            closestRetailer: null,
            farthestRetailer: null,
          },
        };
      }

      // Process regional data
      const userProducts = regionalData.filter((p: any) => p.is_user_product);
      const competitorProducts = regionalData.filter(
        (p: any) => !p.is_user_product
      );

      // Calculate distance statistics
      const distances = competitorProducts
        .map((p: any) => p.distance_miles)
        .filter((d: any) => d !== undefined);
      const averageDistance =
        distances.length > 0
          ? distances.reduce((sum: number, d: number) => sum + d, 0) /
            distances.length
          : 0;
      const minDistance = distances.length > 0 ? Math.min(...distances) : 0;
      const maxDistance = distances.length > 0 ? Math.max(...distances) : 0;

      // Get unique retailers with distance info
      const retailerInfo = competitorProducts.reduce(
        (acc: Record<string, any>, p: any) => {
          if (!acc[p.retailer_id]) {
            acc[p.retailer_id] = {
              name: p.retailer_name,
              city: p.retailer_city,
              state: p.retailer_state,
              distance: p.distance_miles,
              productCount: 0,
            };
          }
          acc[p.retailer_id].productCount++;
          return acc;
        },
        {}
      );

      const retailersWithDistance = Object.entries(retailerInfo)
        .map(([id, data]: [string, any]) => ({
          retailerId: id,
          ...data,
        }))
        .sort((a, b) => a.distance - b.distance); // Sort by distance

      const closestRetailer = retailersWithDistance[0] || null;
      const farthestRetailer =
        retailersWithDistance[retailersWithDistance.length - 1] || null;

      // Category analysis with distance context
      const availableCategories = [
        ...new Set(competitorProducts.map((p: any) => p.category)),
      ].filter(Boolean) as string[];

      const hotCategories = availableCategories
        .map((category: string) => {
          const categoryProducts = competitorProducts.filter(
            (p: any) => p.category.toLowerCase() === category.toLowerCase()
          );
          if (categoryProducts.length === 0) return null;

          const weightCounts = categoryProducts.reduce(
            (acc: Record<string, number>, p: any) => {
              if (p.display_weight) {
                acc[p.display_weight] = (acc[p.display_weight] || 0) + 1;
              }
              return acc;
            },
            {} as Record<string, number>
          );

          const topWeights = Object.keys(weightCounts)
            .sort((a, b) => weightCounts[b] - weightCounts[a])
            .slice(0, 3);

          const weightAnalysis = topWeights
            .map((weight) => {
              const weightProducts = categoryProducts.filter(
                (p: any) => p.display_weight === weight
              );

              if (weightProducts.length === 0) return null;

              const prices = weightProducts.map((p: any) =>
                Number(p.latest_price)
              );
              const distances = weightProducts
                .map((p: any) => p.distance_miles)
                .filter((d: any) => d !== undefined);

              const marketAvg =
                prices.reduce((sum: number, price: number) => sum + price, 0) /
                prices.length;
              const marketMin = Math.min(...prices);
              const marketMax = Math.max(...prices);
              const avgDistance =
                distances.length > 0
                  ? distances.reduce((sum: number, d: number) => sum + d, 0) /
                    distances.length
                  : 0;
              const closestDistance =
                distances.length > 0 ? Math.min(...distances) : 0;

              // User comparison
              const userWeightProducts = userProducts.filter(
                (p: any) =>
                  p.category.toLowerCase() === category.toLowerCase() &&
                  p.display_weight === weight
              );
              const yourAvg =
                userWeightProducts.length > 0
                  ? userWeightProducts.reduce(
                      (sum: number, p: any) => sum + Number(p.latest_price),
                      0
                    ) / userWeightProducts.length
                  : 0;

              const youVsMarketPct =
                yourAvg > 0 ? ((yourAvg - marketAvg) / marketAvg) * 100 : 0;

              const retailerCount = new Set(
                weightProducts.map((p: any) => p.retailer_id)
              ).size;

              return {
                category,
                weight,
                marketAvg: parseFloat(marketAvg.toFixed(2)),
                marketMin: parseFloat(marketMin.toFixed(2)),
                marketMax: parseFloat(marketMax.toFixed(2)),
                yourAvg: parseFloat(yourAvg.toFixed(2)),
                youVsMarketPct: parseFloat(youVsMarketPct.toFixed(2)),
                retailerCount,
                productCount: weightProducts.length,
                averageDistance: parseFloat(avgDistance.toFixed(1)),
                closestDistance: parseFloat(closestDistance.toFixed(1)),
                priceSpread: parseFloat((marketMax - marketMin).toFixed(2)),
              };
            })
            .filter(Boolean);

          return weightAnalysis;
        })
        .filter(Boolean)
        .flat()
        .sort((a, b) => (b?.productCount || 0) - (a?.productCount || 0));

      // Distance-based insights
      const insights = [
        `Analyzed ${competitorProducts.length} products from ${retailersWithDistance.length} retailers within ${radiusMiles} miles.`,
        `Average distance to competitors: ${averageDistance.toFixed(1)} miles.`,
      ];

      if (closestRetailer) {
        insights.push(
          `Closest competitor: ${
            closestRetailer.name
          } at ${closestRetailer.distance.toFixed(1)} miles.`
        );
      }

      if (retailersWithDistance.length > 5) {
        insights.push(
          `Dense market area with ${retailersWithDistance.length} competitors in the radius.`
        );
      } else if (retailersWithDistance.length <= 2) {
        insights.push(
          `Limited competition with only ${retailersWithDistance.length} competitors in the area.`
        );
      }

      // Distance-based price analysis
      const nearbyCategories = hotCategories.filter(
        (c) => c && c.closestDistance <= radiusMiles * 0.3
      ); // Within 30% of radius
      if (nearbyCategories.length > 0) {
        insights.push(
          `${
            nearbyCategories.length
          } categories have nearby competitors within ${(
            radiusMiles * 0.3
          ).toFixed(1)} miles.`
        );
      }

      // Regional recommendations with distance context
      const recommendations = [
        "Radius-based analysis helps understand your immediate competitive landscape.",
        "Consider both distance and pricing when setting competitive strategies.",
      ];

      if (averageDistance > radiusMiles * 0.7) {
        recommendations.push(
          `Most competitors are far from your location - you may have geographic pricing flexibility.`
        );
      } else if (averageDistance < radiusMiles * 0.3) {
        recommendations.push(
          `High competitor density nearby - focus on differentiation and competitive pricing.`
        );
      }

      if (userRetailerId && userProducts.length > 0) {
        const userDistances = userProducts
          .map((p: any) => p.distance_miles)
          .filter((d: any) => d !== undefined);
        if (userDistances.length > 0 && userDistances[0] > 0) {
          recommendations.push(
            `Your location is ${userDistances[0].toFixed(
              1
            )} miles from the analysis center.`
          );
        }
      }

      return {
        analysisType: "regional-radius",
        location: {
          latitude: centerLatitude,
          longitude: centerLongitude,
          radiusMiles,
        },
        competitorCount: retailersWithDistance.length,
        hotCategories: hotCategories.slice(0, 20),
        gaps: [], // Regional analysis focuses more on market overview
        insights,
        recommendations,
        regionStats: {
          totalRetailers: retailersWithDistance.length,
          totalProducts: competitorProducts.length,
          averageDistance: parseFloat(averageDistance.toFixed(1)),
          closestRetailer: closestRetailer
            ? {
                name: closestRetailer.name,
                distance: parseFloat(closestRetailer.distance.toFixed(1)),
                city: closestRetailer.city,
              }
            : null,
          farthestRetailer: farthestRetailer
            ? {
                name: farthestRetailer.name,
                distance: parseFloat(farthestRetailer.distance.toFixed(1)),
                city: farthestRetailer.city,
              }
            : null,
        },
        generatedAt: new Date().toISOString(),
        totalProductsAnalyzed: competitorProducts.length,
        categoriesFound: availableCategories.length,
      };
    } catch (error) {
      logger.error("Error in regional market analysis by radius:", error);
      throw error;
    }
  }
}

// Singleton instance
export const competitorService = new CompetitorService();
