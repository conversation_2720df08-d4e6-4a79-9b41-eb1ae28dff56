#!/usr/bin/env node

/**
 * Test script to verify the new business search functionality
 * This script tests both the competitor search (database only) and business search (with Google Places fallback)
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_QUERIES = {
  // Test cases for business search (should include Google Places fallback)
  business: [
    'CA', // < 3 words - should search city/state
    'California', // < 3 words - should search city/state  
    'Green Dispensary Los Angeles', // >= 3 words - should search name field
    'Cannabis Store San Francisco', // >= 3 words - should search name field
    'Nonexistent Business Name That Should Not Exist', // Should fallback to Google Places
  ],
  // Test cases for competitor search (database only)
  competitor: [
    'CA',
    'California', 
    'Green Dispensary Los Angeles',
    'Cannabis Store San Francisco',
    'Nonexistent Business Name That Should Not Exist', // Should return empty, no Google fallback
  ]
};

async function testEndpoint(endpoint, query, description) {
  try {
    console.log(`\n🔍 Testing ${description}: "${query}"`);
    console.log(`   Endpoint: ${endpoint}`);
    
    const response = await axios.get(`${BASE_URL}${endpoint}?query=${encodeURIComponent(query)}`, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const results = response.data.retailers || [];
    console.log(`   ✅ Status: ${response.status}`);
    console.log(`   📊 Results: ${results.length} retailers found`);
    
    if (results.length > 0) {
      console.log(`   📍 First result: ${results[0].dispensary_name || results[0].name} (${results[0].city}, ${results[0].state})`);
      console.log(`   🏪 From database: ${results[0].isFromOurDatabase !== false ? 'Yes' : 'No (Google Places)'}`);
    }
    
    return {
      success: true,
      count: results.length,
      hasGoogleResults: results.some(r => r.isFromOurDatabase === false),
      results: results.slice(0, 2) // Keep first 2 for analysis
    };
  } catch (error) {
    console.log(`   ❌ Error: ${error.response?.status || error.code} - ${error.message}`);
    return {
      success: false,
      error: error.message,
      status: error.response?.status
    };
  }
}

async function runTests() {
  console.log('🚀 Starting Business Search vs Competitor Search Test');
  console.log('=' .repeat(60));
  
  const results = {
    business: {},
    competitor: {}
  };
  
  // Test business search endpoint
  console.log('\n📈 TESTING BUSINESS SEARCH ENDPOINT (with Google Places fallback)');
  console.log('-'.repeat(60));
  
  for (const query of TEST_QUERIES.business) {
    const result = await testEndpoint(
      '/api/misc/retailers/business-search',
      query,
      'Business Search'
    );
    results.business[query] = result;
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Test competitor search endpoint  
  console.log('\n🏪 TESTING COMPETITOR SEARCH ENDPOINT (database only)');
  console.log('-'.repeat(60));
  
  for (const query of TEST_QUERIES.competitor) {
    const result = await testEndpoint(
      '/api/misc/retailers/search',
      query,
      'Competitor Search'
    );
    results.competitor[query] = result;
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Analysis
  console.log('\n📊 ANALYSIS');
  console.log('=' .repeat(60));
  
  console.log('\n🔍 Word Count Logic Test:');
  const shortQueries = ['CA', 'California'];
  const longQueries = ['Green Dispensary Los Angeles', 'Cannabis Store San Francisco'];
  
  shortQueries.forEach(query => {
    const businessResult = results.business[query];
    if (businessResult.success) {
      console.log(`   ✅ "${query}" (${query.split(' ').length} words): ${businessResult.count} results`);
    }
  });
  
  longQueries.forEach(query => {
    const businessResult = results.business[query];
    if (businessResult.success) {
      console.log(`   ✅ "${query}" (${query.split(' ').length} words): ${businessResult.count} results`);
    }
  });
  
  console.log('\n🌐 Google Places Fallback Test:');
  const nonexistentQuery = 'Nonexistent Business Name That Should Not Exist';
  const businessResult = results.business[nonexistentQuery];
  const competitorResult = results.competitor[nonexistentQuery];
  
  if (businessResult.success && competitorResult.success) {
    console.log(`   Business Search: ${businessResult.count} results (Google fallback: ${businessResult.hasGoogleResults ? 'Yes' : 'No'})`);
    console.log(`   Competitor Search: ${competitorResult.count} results (Should be 0 - database only)`);
    
    if (businessResult.count > competitorResult.count) {
      console.log('   ✅ Business search correctly uses Google Places fallback');
    } else {
      console.log('   ⚠️  Google Places fallback may not be working as expected');
    }
  }
  
  console.log('\n✅ Test completed!');
  console.log('\nExpected behavior:');
  console.log('- Business search should return more results due to Google Places fallback');
  console.log('- Competitor search should only return database results');
  console.log('- Short queries (< 3 words) should search city/state fields');
  console.log('- Long queries (>= 3 words) should search name fields');
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testEndpoint };
