# Business Search Implementation

## Overview

This implementation separates business search functionality from competitor search functionality in the onboarding process. The key difference is that business search includes Google Places fallback while competitor search remains database-only.

## Changes Made

### 1. Backend Changes

#### SupabaseService (`apps/platform/src/supabase/SupabaseService.ts`)
- **Added `searchRetailersByNameOnly()`**: Searches only in name and dispensary_name fields (for >= 3 words)
- **Added `searchRetailersByCityState()`**: Searches only in city and state fields (for < 3 words)
- **Modified existing `searchRetailers()`**: Now documented as competitor search (database only)

#### CompetitorService (`apps/platform/src/competitors/CompetitorService.ts`)
- **Modified `searchRetailers()`**: Now database-only for competitor search
- **Added `searchBusinessRetailers()`**: Enhanced business search with word count logic and cannabis-focused Google Places fallback
- **Added `searchCannabisBusinessInGooglePlaces()`**: Cannabis-specific Google Places search with context enhancement
- **Added `buildCannabisSearchQueries()`**: Builds multiple search queries with cannabis terms
- **Added `filterCannabisRelatedResults()`**: Filters Google Places results to prioritize cannabis businesses

#### MiscController (`apps/platform/src/misc/MiscController.ts`)
- **Modified `/api/misc/retailers/search`**: Now documented as competitor search (database only)
- **Added `/api/misc/retailers/business-search`**: New endpoint for business search with enhanced logic

### 2. Frontend Changes

#### Firestore Service (`apps/ui/src/services/firestore.ts`)
- **Added `searchBusinessRetailersSupabase()`**: New function that calls the business search endpoint
- **Updated `searchRetailersSupabase()`**: Now documented as competitor search

#### BusinessLookupDummy (`apps/ui/src/views/location/BusinessLookupDummy.tsx`)
- **Updated search logic**: Now uses `searchBusinessRetailersSupabase()` instead of `searchRetailersSupabase()`
- **Updated manual entry logic**: Also uses the new business search function

## Cannabis Business Context (CBR)

The enhanced business search specifically targets Cannabis Business Related (CBR) establishments when falling back to Google Places:

### Cannabis Search Enhancement Features:
1. **Multiple Cannabis Queries**: Generates multiple search variations with cannabis terms
2. **Cannabis Keywords**: Includes "dispensary", "cannabis", "marijuana", "weed", "cbd", "hemp", etc.
3. **Location Bias**: Focuses on US locations where cannabis is more likely to be legal
4. **Result Filtering**: Prioritizes businesses with cannabis-related keywords in their names/descriptions
5. **Contextual Search**: Adds cannabis context to generic queries (e.g., "Los Angeles" becomes "dispensary in Los Angeles")

### Cannabis Search Query Examples:
- Original: "Los Angeles" → Enhanced: ["Los Angeles dispensary", "cannabis Los Angeles", "dispensary in Los Angeles"]
- Original: "Green Store" → Enhanced: ["Green Store dispensary", "Green Store cannabis", "cannabis dispensary Green Store"]

## Search Logic

### Business Search (`/api/misc/retailers/business-search`)
**Enhanced 3-Step Search Logic**:
1. **Step 1 - Name Search**: Always search retailer name field first
2. **Step 2 - Location Search**: If < 3 results, add city/state search results to existing results
3. **Step 3 - Cannabis Google Places**: If still < 3 results, add Google Places API results with cannabis business context
   - Adds cannabis-related terms: "dispensary", "cannabis", "marijuana", etc.
   - Filters results to prioritize cannabis-related businesses
   - Uses multiple search queries with cannabis context
4. **Result Combination**: Returns combined results from all applicable steps (up to limit)
5. **Used by**: Company Information step in onboarding

### Competitor Search (`/api/misc/retailers/search`)
1. **Database Only**: Only searches Supabase database
2. **No Google Places Fallback**: Returns empty results if nothing found in database
3. **Used by**: Add More Competitors step in onboarding

## API Endpoints

### Business Search
```
GET /api/misc/retailers/business-search?query={searchQuery}
```
- Enhanced search logic with word count analysis
- Google Places fallback if no database results
- Returns retailers from database or Google Places

### Competitor Search  
```
GET /api/misc/retailers/search?query={searchQuery}
```
- Database-only search
- No Google Places fallback
- Returns only retailers from Supabase database

## Testing

A test script has been created (`test-business-search.js`) to verify:
1. Word count logic works correctly
2. Google Places fallback works for business search
3. Competitor search remains database-only
4. Both endpoints return appropriate results

### Running Tests
```bash
node test-business-search.js
```

## Expected Behavior

### Business Search (Company Information)
**3-Step Progressive Search**:
- **Step 1**: All queries search name fields first (e.g., "MedMen", "Green Dispensary", "CA")
- **Step 2**: If < 3 results, adds city/state search results (e.g., "CA" finds California dispensaries)
- **Step 3**: If still < 3 results, adds cannabis-focused Google Places results
- **Result Quality**: Combines database accuracy with Google Places coverage
- **Cannabis Context**: Google Places searches include "dispensary", "cannabis", "marijuana" terms
- **Filtering**: Results prioritize cannabis-related businesses
- **Comprehensive Results**: Should return more complete cannabis business results

### Competitor Search (Add More Competitors)
- Uses same search logic as before
- Only searches database, no Google Places fallback
- Should return fewer, more targeted results from existing database

## Files Modified

### Backend
- `apps/platform/src/supabase/SupabaseService.ts`
- `apps/platform/src/competitors/CompetitorService.ts`
- `apps/platform/src/misc/MiscController.ts`

### Frontend
- `apps/ui/src/services/firestore.ts`
- `apps/ui/src/views/location/BusinessLookupDummy.tsx`

### Testing
- `test-business-search.js` (new)
- `BUSINESS_SEARCH_IMPLEMENTATION.md` (new)

## Backward Compatibility

- Existing competitor search functionality remains unchanged
- All existing API endpoints continue to work as before
- Only the business search in onboarding now uses enhanced logic
- No breaking changes to existing code
