# Business Search Implementation

## Overview

This implementation separates business search functionality from competitor search functionality in the onboarding process. The key difference is that business search includes Google Places fallback while competitor search remains database-only.

## Changes Made

### 1. Backend Changes

#### SupabaseService (`apps/platform/src/supabase/SupabaseService.ts`)
- **Added `searchRetailersByNameOnly()`**: Searches only in name and dispensary_name fields (for >= 3 words)
- **Added `searchRetailersByCityState()`**: Searches only in city and state fields (for < 3 words)
- **Modified existing `searchRetailers()`**: Now documented as competitor search (database only)

#### CompetitorService (`apps/platform/src/competitors/CompetitorService.ts`)
- **Modified `searchRetailers()`**: Now database-only for competitor search
- **Added `searchBusinessRetailers()`**: Enhanced business search with word count logic and Google Places fallback

#### MiscController (`apps/platform/src/misc/MiscController.ts`)
- **Modified `/api/misc/retailers/search`**: Now documented as competitor search (database only)
- **Added `/api/misc/retailers/business-search`**: New endpoint for business search with enhanced logic

### 2. Frontend Changes

#### Firestore Service (`apps/ui/src/services/firestore.ts`)
- **Added `searchBusinessRetailersSupabase()`**: New function that calls the business search endpoint
- **Updated `searchRetailersSupabase()`**: Now documented as competitor search

#### BusinessLookupDummy (`apps/ui/src/views/location/BusinessLookupDummy.tsx`)
- **Updated search logic**: Now uses `searchBusinessRetailersSupabase()` instead of `searchRetailersSupabase()`
- **Updated manual entry logic**: Also uses the new business search function

## Search Logic

### Business Search (`/api/misc/retailers/business-search`)
1. **Word Count Analysis**: 
   - If query has < 3 words → Search city/state fields in database
   - If query has ≥ 3 words → Search name fields in database
2. **Google Places Fallback**: If no results from database, search Google Places API
3. **Used by**: Company Information step in onboarding

### Competitor Search (`/api/misc/retailers/search`)
1. **Database Only**: Only searches Supabase database
2. **No Google Places Fallback**: Returns empty results if nothing found in database
3. **Used by**: Add More Competitors step in onboarding

## API Endpoints

### Business Search
```
GET /api/misc/retailers/business-search?query={searchQuery}
```
- Enhanced search logic with word count analysis
- Google Places fallback if no database results
- Returns retailers from database or Google Places

### Competitor Search  
```
GET /api/misc/retailers/search?query={searchQuery}
```
- Database-only search
- No Google Places fallback
- Returns only retailers from Supabase database

## Testing

A test script has been created (`test-business-search.js`) to verify:
1. Word count logic works correctly
2. Google Places fallback works for business search
3. Competitor search remains database-only
4. Both endpoints return appropriate results

### Running Tests
```bash
node test-business-search.js
```

## Expected Behavior

### Business Search (Company Information)
- Short queries like "CA" or "California" search city/state fields
- Long queries like "Green Dispensary Los Angeles" search name fields  
- If no database results, falls back to Google Places API
- Should return more comprehensive results

### Competitor Search (Add More Competitors)
- Uses same search logic as before
- Only searches database, no Google Places fallback
- Should return fewer, more targeted results from existing database

## Files Modified

### Backend
- `apps/platform/src/supabase/SupabaseService.ts`
- `apps/platform/src/competitors/CompetitorService.ts`
- `apps/platform/src/misc/MiscController.ts`

### Frontend
- `apps/ui/src/services/firestore.ts`
- `apps/ui/src/views/location/BusinessLookupDummy.tsx`

### Testing
- `test-business-search.js` (new)
- `BUSINESS_SEARCH_IMPLEMENTATION.md` (new)

## Backward Compatibility

- Existing competitor search functionality remains unchanged
- All existing API endpoints continue to work as before
- Only the business search in onboarding now uses enhanced logic
- No breaking changes to existing code
